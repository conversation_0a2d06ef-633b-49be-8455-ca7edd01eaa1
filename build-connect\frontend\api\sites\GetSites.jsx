import { privateAPIClient } from '../index'; // Adjust path to your API client

export const fetchSites = async (filters = {}) => {
    try {
        const query = new URLSearchParams();

        // Append filters if provided
        if (filters.search) query.append('search', filters.search);
        if (filters.state) query.append('state', filters.state);
        if (filters.district) query.append('district', filters.district);
        if (filters.pincode) query.append('pincode', filters.pincode);
        if (filters.latitude) query.append('latitude', filters.latitude);
        if (filters.longitude) query.append('longitude', filters.longitude);
        if (filters.distance) query.append('distance', filters.distance);
        if (filters.sortBy) query.append('sortBy', filters.sortBy);
        if (filters.order) query.append('order', filters.order);
        if (filters.page) query.append('page', filters.page);
        if (filters.limit) query.append('limit', filters.limit);

        const url = `/site-service/api/v1/sites?${query.toString()}`;

        const response = await privateAPIClient.get(url);
        return response.data.sites || [];
    } catch (err) {
        console.error(err);
        throw new Error('Failed to fetch sites. Please try again.');
    }
};

// 1. Default
// fetchSites();

// 2. Text search only
// fetchSites({ search: 'beachfront' });

// 3. Search + State + Sort
// fetchSites({ search: 'beachfront', state: 'Karnataka', sortBy: 'price', order: 'asc' });

// 4. State + District
// fetchSites({ state: 'Karnataka', district: 'Dakshina Kannada' });

// 5. Pin-code + paging
// fetchSites({ pincode: '574142', page: 2, limit: 25 });

// 6. Geo-radius + distance sort
// fetchSites({ latitude: 12.9716, longitude: 77.5946, distance: 5000, sortBy: 'distance', order: 'asc' });

// 7. Geo + Search + State
// fetchSites({ latitude: 13.0214, longitude: 74.8570, distance: 10000, search: 'corner plot', state: 'Karnataka' });

// 8. District + price asc, limit
// fetchSites({ district: 'Udupi', sortBy: 'price', order: 'asc', limit: 5 });

// 9. All filters
// fetchSites({
// search: 'lake view',
// state: 'Karnataka',
// district: 'Mysuru',
// pincode: '570001',
// latitude: 12.3052,
// longitude: 76.6552,
// distance: 8000,
// sortBy: 'price',
// order: 'desc',
// page: 3,
// limit: 10
// });
