import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "BUILD-CONNECT - Connect. Build. Grow.",
  description:
    "A comprehensive platform connecting property buyers, site scouts, contractors, and facilitating real estate transactions.",
  keywords: [
    "real estate",
    "property",
    "construction",
    "contractors",
    "brokers",
    "site scouts",
  ],
  authors: [{ name: "BUILD-CONNECT Team" }],
  creator: "BUILD-CONNECT",
  publisher: "BUILD-CONNECT",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <div className="relative flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
