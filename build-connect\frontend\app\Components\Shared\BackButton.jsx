// components/BackButton.js
import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from '@expo/vector-icons/Ionicons';

export default function BackButton({ color = '#000', size = 24, style }) {
    const navigation = useNavigation();
    return (
        <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={[styles.button, style]}
            accessibilityLabel="Go back"
        >
            <Ionicons name="arrow-back" size={size} color={color} />
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        padding: 12,
        justifyContent: 'left',
        alignItems: 'left',
        color: 'transparent',
    },
});
