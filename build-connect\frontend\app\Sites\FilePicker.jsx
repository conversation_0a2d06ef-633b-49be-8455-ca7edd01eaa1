import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';

const FilePicker = ({
    label,
    files,
    setFiles,
    keyName,
    maxFiles,
    allowedTypes,
    maxFileSize,
    isMultiple = false,
    theme,
    onDocumentPreview,
}) => {
    // Filter out files with mimeType: 'existing' for UI display
    const displayFiles = isMultiple
        ? Array.isArray(files)
            ? files.filter((file) => file.mimeType !== 'existing')
            : []
        : files?.mimeType !== 'existing'
          ? files
          : null;

    const pickDoc = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: allowedTypes,
            });
            if (res.canceled) return;
            const asset = res.assets[0];

            if (asset.size > maxFileSize) {
                alert('File too large', 'Max 5 MB allowed.');
                return;
            }
            if (!allowedTypes.includes(asset.mimeType)) {
                alert('Invalid type', 'Choose JPG, PNG, or PDF');
                return;
            }

            if (isMultiple && displayFiles.length >= maxFiles) {
                alert('Limit reached', `Maximum ${maxFiles} files allowed.`);
                return;
            }

            if (isMultiple) {
                setFiles([...files, asset]);
            } else {
                setFiles(asset);
            }
        } catch {
            alert('Error', 'Failed to select file');
        }
    };

    const deleteImage = (index) => {
        if (isMultiple) {
            setFiles(files.filter((_, i) => i !== index));
        } else {
            setFiles(null);
        }
    };

    return (
        <View>
            <TouchableOpacity
                style={[
                    styles.button,
                    {
                        backgroundColor: theme?.INPUT_BACKGROUND || '#f5f5f5',
                        borderColor: (
                            isMultiple ? displayFiles.length > 0 : displayFiles
                        )
                            ? theme?.PRIMARY || '#008060'
                            : theme?.INPUT_BORDER || '#ddd',
                    },
                ]}
                onPress={pickDoc}
            >
                <Ionicons
                    name={
                        (isMultiple ? displayFiles.length > 0 : displayFiles)
                            ? 'checkmark-circle'
                            : 'document-attach'
                    }
                    size={20}
                    color={
                        (isMultiple ? displayFiles.length > 0 : displayFiles)
                            ? theme?.PRIMARY || '#008060'
                            : theme?.TEXT_SECONDARY || '#555'
                    }
                    style={styles.buttonIcon}
                />
                <Text
                    style={[
                        styles.buttonText,
                        {
                            color: (
                                isMultiple
                                    ? displayFiles.length > 0
                                    : displayFiles
                            )
                                ? theme?.PRIMARY || '#008060'
                                : theme?.TEXT_PRIMARY || '#333',
                        },
                    ]}
                >
                    {isMultiple
                        ? displayFiles.length > 0
                            ? `✓ ${displayFiles.length} image${displayFiles.length > 1 ? 's' : ''} selected`
                            : label
                        : displayFiles
                          ? `✓ ${displayFiles.name}`
                          : label}
                </Text>
            </TouchableOpacity>
            {isMultiple && displayFiles.length > 0 && (
                <View style={styles.imageList}>
                    {displayFiles.map((image, index) => (
                        <View key={index} style={styles.imageItem}>
                            <Text style={styles.imageText}>{image.name}</Text>
                            <TouchableOpacity
                                onPress={() => deleteImage(index)}
                            >
                                <Ionicons
                                    name="trash-outline"
                                    size={20}
                                    color="#ff4444"
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
        padding: 12,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    buttonSelected: {
        backgroundColor: '#e6f4f1',
        borderColor: '#008060',
    },
    buttonIcon: {
        marginRight: 8,
    },
    buttonText: {
        fontSize: 16,
        color: '#555',
    },
    buttonTextSelected: {
        color: '#008060',
        fontWeight: '500',
    },
    imageList: {
        marginTop: 8,
        marginBottom: 16,
    },
    imageItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    imageText: {
        fontSize: 14,
        color: '#333',
        flex: 1,
    },
});

export default FilePicker;
