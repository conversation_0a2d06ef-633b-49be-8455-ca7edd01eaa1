import * as Yup from 'yup';

// Site Details Step Validation
export const siteDetailsSchema = Yup.object().shape({
    name: Yup.string()
        .min(3, 'Site name must be at least 3 characters')
        .max(100, 'Site name must not exceed 100 characters')
        .required('Site name is required'),
    addressLine1: Yup.string()
        .min(5, 'Address must be at least 5 characters')
        .max(200, 'Address must not exceed 200 characters')
        .required('Address line 1 is required'),
    addressLine2: Yup.string()
        .max(200, 'Address must not exceed 200 characters')
        .optional(),
    landmark: Yup.string()
        .max(100, 'Landmark must not exceed 100 characters')
        .optional(),
    pincode: Yup.string()
        .matches(/^\d{6}$/, 'Enter a valid 6-digit pincode')
        .required('Pincode is required'),
    state: Yup.string()
        .min(2, 'State must be at least 2 characters')
        .max(50, 'State must not exceed 50 characters')
        .required('State is required'),
    district: Yup.string()
        .min(2, 'District must be at least 2 characters')
        .max(50, 'District must not exceed 50 characters')
        .required('District is required'),
    plotArea: Yup.number()
        .typeError('Plot area must be a valid number')
        .positive('Plot area must be positive')
        .min(100, 'Plot area must be at least 100 sqft')
        .max(1000000, 'Plot area must not exceed 10 lakh sqft')
        .required('Plot area is required'),
    price: Yup.number()
        .typeError('Price must be a valid number')
        .positive('Price must be positive')
        .min(10000, 'Price must be at least ₹10,000')
        .max(100000000, 'Price must not exceed ₹10 crore')
        .optional(),
    siteImages: Yup.array()
        .min(1, 'At least one site image is required')
        .max(10, 'Maximum 10 images allowed')
        .required('Site images are required'),
});

// Location Step Validation
export const locationSchema = Yup.object().shape({
    location: Yup.string()
        .min(3, 'Location must be at least 3 characters')
        .max(100, 'Location must not exceed 100 characters')
        .required('Location is required'),
    latitude: Yup.number()
        .typeError('Latitude must be a valid number')
        .min(-90, 'Latitude must be between -90 and 90')
        .max(90, 'Latitude must be between -90 and 90')
        .required('Latitude is required'),
    longitude: Yup.number()
        .typeError('Longitude must be a valid number')
        .min(-180, 'Longitude must be between -180 and 180')
        .max(180, 'Longitude must be between -180 and 180')
        .required('Longitude is required'),
    village: Yup.string()
        .min(2, 'Village must be at least 2 characters')
        .max(50, 'Village must not exceed 50 characters')
        .required('Village is required'),
    surveyNumber: Yup.string()
        .min(1, 'Survey number must be at least 1 character')
        .max(50, 'Survey number must not exceed 50 characters')
        .required('Survey number is required'),
});

// Encumbrance Step Validation
export const encumbranceSchema = Yup.object().shape({
    encOwnerName: Yup.string()
        .min(2, 'Owner name must be at least 2 characters')
        .max(100, 'Owner name must not exceed 100 characters')
        .matches(/^[a-zA-Z\s.]+$/, 'Owner name can only contain letters, spaces, and dots')
        .required('Owner name is required'),
    encumbranceDocNumber: Yup.string()
        .min(5, 'Document number must be at least 5 characters')
        .max(50, 'Document number must not exceed 50 characters')
        .required('Document number is required'),
    encumbranceDate: Yup.string()
        .matches(
            /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/,
            'Date must be in DD/MM/YYYY format'
        )
        .required('Encumbrance date is required'),
    encumbranceCert: Yup.mixed()
        .required('Encumbrance certificate is required'),
});

// Property Tax Step Validation
export const propertyTaxSchema = Yup.object().shape({
    propertyTaxNumber: Yup.string()
        .min(5, 'Property tax number must be at least 5 characters')
        .max(50, 'Property tax number must not exceed 50 characters')
        .required('Property tax number is required'),
    propertyTaxDate: Yup.string()
        .matches(
            /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/,
            'Date must be in DD/MM/YYYY format'
        )
        .required('Property tax date is required'),
    propertyTaxRec: Yup.mixed()
        .required('Property tax receipt is required'),
});

// Complete Site Validation Schema
export const completeSiteSchema = Yup.object().shape({
    // Site Details
    name: Yup.string()
        .min(3, 'Site name must be at least 3 characters')
        .max(100, 'Site name must not exceed 100 characters')
        .required('Site name is required'),
    addressLine1: Yup.string()
        .min(5, 'Address must be at least 5 characters')
        .max(200, 'Address must not exceed 200 characters')
        .required('Address line 1 is required'),
    pincode: Yup.string()
        .matches(/^\d{6}$/, 'Enter a valid 6-digit pincode')
        .required('Pincode is required'),
    state: Yup.string()
        .min(2, 'State must be at least 2 characters')
        .max(50, 'State must not exceed 50 characters')
        .required('State is required'),
    district: Yup.string()
        .min(2, 'District must be at least 2 characters')
        .max(50, 'District must not exceed 50 characters')
        .required('District is required'),
    plotArea: Yup.number()
        .typeError('Plot area must be a valid number')
        .positive('Plot area must be positive')
        .min(100, 'Plot area must be at least 100 sqft')
        .max(1000000, 'Plot area must not exceed 10 lakh sqft')
        .required('Plot area is required'),
    siteImages: Yup.array()
        .min(1, 'At least one site image is required')
        .max(10, 'Maximum 10 images allowed')
        .required('Site images are required'),
    
    // Location Details
    location: Yup.string()
        .min(3, 'Location must be at least 3 characters')
        .max(100, 'Location must not exceed 100 characters')
        .required('Location is required'),
    latitude: Yup.number()
        .typeError('Latitude must be a valid number')
        .min(-90, 'Latitude must be between -90 and 90')
        .max(90, 'Latitude must be between -90 and 90')
        .required('Latitude is required'),
    longitude: Yup.number()
        .typeError('Longitude must be a valid number')
        .min(-180, 'Longitude must be between -180 and 180')
        .max(180, 'Longitude must be between -180 and 180')
        .required('Longitude is required'),
    village: Yup.string()
        .min(2, 'Village must be at least 2 characters')
        .max(50, 'Village must not exceed 50 characters')
        .required('Village is required'),
    surveyNumber: Yup.string()
        .min(1, 'Survey number must be at least 1 character')
        .max(50, 'Survey number must not exceed 50 characters')
        .required('Survey number is required'),
    
    // Encumbrance Details
    encOwnerName: Yup.string()
        .min(2, 'Owner name must be at least 2 characters')
        .max(100, 'Owner name must not exceed 100 characters')
        .matches(/^[a-zA-Z\s.]+$/, 'Owner name can only contain letters, spaces, and dots')
        .required('Owner name is required'),
    encumbranceDocNumber: Yup.string()
        .min(5, 'Document number must be at least 5 characters')
        .max(50, 'Document number must not exceed 50 characters')
        .required('Document number is required'),
    encumbranceDate: Yup.string()
        .matches(
            /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/,
            'Date must be in DD/MM/YYYY format'
        )
        .required('Encumbrance date is required'),
    encumbranceCert: Yup.mixed()
        .required('Encumbrance certificate is required'),
    
    // Property Tax Details
    propertyTaxNumber: Yup.string()
        .min(5, 'Property tax number must be at least 5 characters')
        .max(50, 'Property tax number must not exceed 50 characters')
        .required('Property tax number is required'),
    propertyTaxDate: Yup.string()
        .matches(
            /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/,
            'Date must be in DD/MM/YYYY format'
        )
        .required('Property tax date is required'),
    propertyTaxRec: Yup.mixed()
        .required('Property tax receipt is required'),
});

// Get validation schema for specific step
export const getStepValidationSchema = (step) => {
    switch (step) {
        case 'siteDetails':
            return siteDetailsSchema;
        case 'location':
            return locationSchema;
        case 'encumbrance':
            return encumbranceSchema;
        case 'propertyTax':
            return propertyTaxSchema;
        case 'review':
            return completeSiteSchema;
        default:
            return siteDetailsSchema;
    }
};

// File constraints
export const FILE_CONSTRAINTS = {
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'application/pdf'],
    MAX_SITE_IMAGES: 10,
};

export default completeSiteSchema;
