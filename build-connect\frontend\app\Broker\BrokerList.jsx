import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    TextInput,
    Image,
    SafeAreaView,
    FlatList,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchAllBrokers } from '../../api/broker/brokerApi';

const BrokerList = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [isSearchBarVisible, setSearchBarVisible] = useState(false);

    const {
        data: brokers,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['allBrokers'],
        queryFn: fetchAllBrokers,
    });

    const filteredBrokers = brokers.filter((broker) => {
        const matchesSearch =
            (broker.name?.toLowerCase() || '').includes(
                searchQuery.toLowerCase()
            ) ||
            (broker.serviceAreas?.join(' ')?.toLowerCase() || '').includes(
                searchQuery.toLowerCase()
            );

        const matchesCategory =
            selectedCategory === 'all' || broker.category === selectedCategory;

        return matchesSearch && matchesCategory;
    });

    const renderBroker = (broker) => (
        <TouchableOpacity
            key={broker.id}
            style={[
                styles.brokerCard,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={() =>
                router.push(`/Profile/BrokerProfile?brokerId=${broker.id}`)
            }
        >
            <View style={styles.brokerHeader}>
                {broker.isAvailable ? (
                    <View style={[styles.available]}>
                        <View
                            style={[
                                styles.availableDot,
                                { backgroundColor: theme.SUCCESS },
                            ]}
                        />
                    </View>
                ) : (
                    <View style={styles.available}>
                        <View
                            style={[
                                styles.availableDot,
                                { backgroundColor: theme.ERROR },
                            ]}
                        />
                    </View>
                )}
                {broker.image ? (
                    <Image
                        source={{ uri: broker.image }}
                        style={styles.brokerImage}
                    />
                ) : (
                    <View
                        style={[
                            styles.brokerImage,
                            { backgroundColor: theme.GRAY_LIGHT + 'CC' },
                        ]}
                    >
                        <Ionicons
                            name="person"
                            size={50}
                            color={theme.TEXT_SECONDARY}
                        />
                    </View>
                )}
                <View style={styles.brokerInfo}>
                    <View style={styles.nameRow}>
                        <Text
                            style={[
                                styles.brokerName,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {broker.name}
                        </Text>
                        <Ionicons
                            name="checkmark-circle-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                    </View>
                    <Text
                        style={[
                            styles.brokerExperience,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {broker.experience} years experience
                    </Text>
                    <View style={styles.ratingRow}>
                        <Ionicons name="star" size={16} color="#FFD700" />
                        <Text
                            style={[
                                styles.rating,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {broker.ratings}
                        </Text>
                    </View>
                </View>
            </View>

            <View style={styles.brokerDetails}>
                <View style={styles.specialtiesContainer}>
                    {broker.serviceAreas
                        .slice(0, 2)
                        .map((serviceArea, index) => (
                            <View
                                key={index}
                                style={[
                                    styles.specialtyTag,
                                    { borderColor: theme.PRIMARY },
                                ]}
                            >
                                <Ionicons
                                    name="location-outline"
                                    size={16}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.specialtyText,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    {serviceArea}
                                </Text>
                            </View>
                        ))}
                    {broker.serviceAreas.length > 2 && (
                        <View
                            style={[
                                styles.specialtyTag,
                                { borderColor: theme.PRIMARY },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.specialtyText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                +{broker.serviceAreas.length - 2}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Find Site Scouts</Text>
                    <TouchableOpacity
                        onPress={() => router.push('/Brokers/BrokerFilters')}
                    >
                        <Ionicons name="filter" size={24} color="#fff" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => setSearchBarVisible(!isSearchBarVisible)}
                    >
                        <Ionicons name="search" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            {isSearchBarVisible && (
                /* Search Bar */
                <View
                    style={[
                        styles.searchContainer,
                        {
                            backgroundColor: theme.CARD,
                            shadowColor: theme.PRIMARY,
                        },
                    ]}
                >
                    <Ionicons
                        name="search"
                        size={20}
                        color={theme.TEXT_SECONDARY}
                    />
                    <TextInput
                        style={[
                            styles.searchInput,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                        placeholder="Search brokers..."
                        placeholderTextColor={theme.TEXT_SECONDARY}
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                    />
                </View>
            )}

            {/* Brokers List */}
            <FlatList
                data={filteredBrokers}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => renderBroker(item)}
                numColumns={2}
                columnWrapperStyle={{
                    justifyContent: 'space-between',
                    paddingHorizontal: 12,
                }}
                contentContainerStyle={{ paddingBottom: 12 }}
            />
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 10,
        paddingBottom: 10,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
        marginHorizontal: 10,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 12,
        elevation: 3,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    searchInput: {
        marginLeft: 10,
        fontSize: 14,
    },
    brokerCard: {
        flex: 1,
        marginTop: 6,
        padding: 8,
        borderRadius: 8,
        elevation: 3,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        maxWidth: '49%',
    },
    brokerHeader: {
        flexDirection: 'column',
        alignItems: 'center',
        marginBottom: 8,
    },
    brokerImage: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 80,
        height: 80,
        borderRadius: 40,
    },
    brokerInfo: {
        alignItems: 'center',
    },
    nameRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
    },
    brokerName: {
        fontSize: 16,
        fontWeight: 'bold',
        marginRight: 8,
    },
    brokerExperience: {
        fontSize: 14,
        marginBottom: 4,
    },
    ratingRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rating: {
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 4,
    },
    brokerDetails: {
        alignItems: 'center',
        marginBottom: 8,
    },
    available: {
        position: 'absolute',
        top: 58,
        right: 36,
        padding: 4,
        fontWeight: '600',
        zIndex: 1,
    },
    availableDot: {
        width: 10,
        height: 10,
        borderRadius: 10,
    },
    specialtiesContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        justifyContent: 'center',
    },

    specialtyTag: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 30,
        marginRight: 8,
        marginBottom: 4,
        borderWidth: 1,
    },
    specialtyText: {
        fontSize: 10,
        fontWeight: '600',
    },
});

export default BrokerList;
