import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';
import FilePicker from './FilePicker';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const EncumbranceStep = ({ theme, formik, setStep, isSubmitting, onDocumentPreview }) => {
    const { values, errors, touched, handleChange, handleBlur, setFieldValue, validateForm, setFieldTouched } = formik;

    const handleNext = async () => {
        const errors = await validateForm();
        const fields = [
            'encOwnerName',
            'encumbranceDocNumber',
            'encumbranceDate',
            'encumbranceCert',
        ];
        if (!fields.some((field) => errors[field])) {
            setStep('propertyTax');
        } else {
            fields.forEach((field) =>
                setFieldTouched(field, true)
            );
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Encumbrance Certificate
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
                Provide encumbrance certificate details and upload the document
            </Text>

            {/* Owner Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.encOwnerName && touched.encOwnerName
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="person-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.encOwnerName}
                    onChangeText={handleChange('encOwnerName')}
                    onBlur={handleBlur('encOwnerName')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter owner name"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Owner name"
                />
            </View>
            {errors.encOwnerName && touched.encOwnerName && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.encOwnerName}
                </Text>
            )}

            {/* Document Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.encumbranceDocNumber && touched.encumbranceDocNumber
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="document-text-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.encumbranceDocNumber}
                    onChangeText={handleChange('encumbranceDocNumber')}
                    onBlur={handleBlur('encumbranceDocNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter document number"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Document number"
                />
            </View>
            {errors.encumbranceDocNumber && touched.encumbranceDocNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.encumbranceDocNumber}
                </Text>
            )}

            {/* Encumbrance Date */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.encumbranceDate && touched.encumbranceDate
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="calendar-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.encumbranceDate}
                    onChangeText={handleChange('encumbranceDate')}
                    onBlur={handleBlur('encumbranceDate')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter encumbrance date (DD/MM/YYYY)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Encumbrance date"
                />
            </View>
            {errors.encumbranceDate && touched.encumbranceDate && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.encumbranceDate}
                </Text>
            )}

            {/* Encumbrance Certificate Upload */}
            <FilePicker
                label="Encumbrance Certificate (JPG/PNG/PDF)"
                files={values.encumbranceCert}
                setFiles={(file) => setFieldValue('encumbranceCert', file)}
                keyName="encumbranceCert"
                maxFiles={1}
                allowedTypes={ALLOWED_TYPES}
                maxFileSize={MAX_FILE_SIZE}
                isMultiple={false}
                theme={theme}
                onDocumentPreview={onDocumentPreview}
            />
            {errors.encumbranceCert && touched.encumbranceCert && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.encumbranceCert}
                </Text>
            )}

            {/* Document Preview */}
            {values.encumbranceCert && (
                <Pressable
                    style={[
                        styles.documentPreview,
                        { backgroundColor: theme.ACCENT, borderColor: theme.INPUT_BORDER }
                    ]}
                    onPress={() => onDocumentPreview(values.encumbranceCert)}
                >
                    <Ionicons
                        name="document-text-outline"
                        size={24}
                        color={theme.PRIMARY}
                    />
                    <Text style={[styles.documentPreviewText, { color: theme.TEXT_PRIMARY }]}>
                        {values.encumbranceCert.name || 'Encumbrance Certificate'}
                    </Text>
                    <Ionicons
                        name="eye-outline"
                        size={20}
                        color={theme.PRIMARY}
                    />
                </Pressable>
            )}

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.backButton, { borderColor: theme.INPUT_BORDER }]}
                    onPress={() => setStep('location')}
                    disabled={isSubmitting}
                    accessibilityLabel="Go back to location details"
                    accessibilityRole="button"
                >
                    <Text style={[styles.backButtonText, { color: theme.PRIMARY }]}>
                        Back
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleNext}
                    disabled={isSubmitting}
                    accessibilityLabel="Proceed to property tax details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default EncumbranceStep;
