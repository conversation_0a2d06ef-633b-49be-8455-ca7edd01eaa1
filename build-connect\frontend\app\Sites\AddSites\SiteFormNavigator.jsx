import React, { useState, createContext } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import SiteDetailsScreen from './SiteDetailsScreen';
import LocationScreen from './LocationScreen';
import EncumbranceScreen from './EncumbranceScreen';
import PropertyTaxScreen from './PropertyTaxScreen';
import SubmitScreen from './SubmitScreen';

const FormContext = createContext();
const Stack = createStackNavigator();

export default function SiteFormNavigator() {
  const [fields, setFields] = useState({
    name: '',
    addressLine1: '',
    addressLine2: '',
    landmark: '',
    location: '',
    pincode: '',
    state: '',
    district: '',
    plotArea: '',
    price: '',
    latitude: '',
    longitude: '',
    encOwnerName: '',
    encDocumentNo: '',
    surveyNo: '',
    village: '',
    subDistrict: '',
    District: '',
    ptrOwnerName: '',
    ptrReciptNo: '',
    siteImages: [],
    encumbranceCert: null,
    propertyTaxRec: null,
  });

  const [region, setRegion] = useState({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  const [marker, setMarker] = useState(null);
  const [hasPermission, setHasPermission] = useState(false);

  return (
    <FormContext.Provider value={{ fields, setFields, region, setRegion, marker, setMarker, hasPermission, setHasPermission }}>
      <Stack.Navigator initialRouteName="SiteDetails">
        <Stack.Screen name="SiteDetails" component={SiteDetailsScreen} options={{ headerTitle: 'Site Details' }} />
        <Stack.Screen name="Location" component={LocationScreen} options={{ headerTitle: 'Location and Site Map' }} />
        <Stack.Screen name="Encumbrance" component={EncumbranceScreen} options={{ headerTitle: 'Encumbrance Certificate' }} />
        <Stack.Screen name="PropertyTax" component={PropertyTaxScreen} options={{ headerTitle: 'Property Tax Receipt' }} />
        <Stack.Screen name="Submit" component={SubmitScreen} options={{ headerTitle: 'Review and Submit' }} />
      </Stack.Navigator>
    </FormContext.Provider>
  );
}

export const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    flexGrow: 1,
  },
  h1: {
    fontSize: 28,
    fontWeight: '700',
    color: '#333',
    marginBottom: 24,
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  section: {
    fontSize: 18,
    fontWeight: '600',
    color: '#008060',
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#008060',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#80c0b0',
  },
  submitButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
  },
  nextButton: {
    backgroundColor: '#008060',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
  },
  nextButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
  },
  backButton: {
    backgroundColor: '#ccc',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  reviewText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 16,
  },
});

export { FormContext };