import React, { useState, useContext } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../../api';
import { FormContext, styles } from './SiteFormNavigator';

const buildPart = (asset) => ({
  uri: asset.uri,
  name: asset.name || `file_${Date.now()}`,
  type: asset.mimeType || 'application/octet-stream',
});

const SubmitScreen = ({ navigation }) => {
  const { fields, setFields, region, setRegion, setMarker, setHasPermission } = useContext(FormContext);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSave = async () => {
    if (!fields.siteImages?.length || !fields.encumbranceCert || !fields.propertyTaxRec) {
      return Alert.alert('Missing docs', 'Please attach at least one site image and the other required documents.');
    }
    if (!fields.name || !fields.pincode || !fields.plotArea) {
      return Alert.alert('Missing fields', 'Name, pincode, plot area are required.');
    }

    const form = new FormData();
    fields.siteImages.forEach((image, index) => {
      form.append('siteImages', buildPart(image));
    });
    form.append('encumbranceCertificate', buildPart(fields.encumbranceCert));
    form.append('propertyTaxReceipt', buildPart(fields.propertyTaxRec));

    Object.entries(fields).forEach(([k, v]) => {
      if (k !== 'siteImages' && k !== 'encumbranceCert' && k !== 'propertyTaxRec') {
        form.append(k, String(v ?? ''));
      }
    });

    try {
      setLoading(true);
      await privateAPIClient.post('/site-service/api/v1/sites', form, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Alert.alert('Success', 'Site created!', [
        {
          text: 'OK',
          onPress: () => router.replace('/Sites'), // Navigate to /Sites
        },
      ]);
      setFields({
        name: '',
        addressLine1: '',
        addressLine2: '',
        landmark: '',
        location: '',
        pincode: '',
        state: '',
        district: '',
        plotArea: '',
        price: '',
        latitude: '',
        longitude: '',
        encOwnerName: '',
        encDocumentNo: '',
        surveyNo: '',
        village: '',
        subDistrict: '',
        District: '',
        ptrOwnerName: '',
        ptrReciptNo: '',
        siteImages: [],
        encumbranceCert: null,
        propertyTaxRec: null,
      });
      setRegion({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });
      setMarker(null);
      setHasPermission(false);
    } catch (err) {
      console.error(err.response?.data || err);
      Alert.alert('Upload failed', 'Check backend logs for details');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.h1}>Review and Submit</Text>
      <View style={styles.card}>
        <Text style={styles.section}>Confirm Details</Text>
        <Text style={styles.reviewText}>Please review all details before submitting.</Text>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.8}
        >
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
          activeOpacity={0.8}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Uploading…' : 'Save Site'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SubmitScreen;