import React from 'react';
import { View } from 'react-native';
import styles from './styles';

const SkeletonLoader = ({ theme }) => (
    <View style={[styles.cardContainer, { backgroundColor: theme.CARD }]}>
        <View
            style={[
                styles.skeletonTitle,
                { backgroundColor: theme.GRAY_LIGHT },
            ]}
        />
        {[...Array(3)].map((_, idx) => (
            <View
                key={idx}
                style={[
                    styles.applicationCard,
                    {
                        backgroundColor: theme.CARD,
                        borderWidth: 0.5,
                        borderColor: theme.GRAY_LIGHT,
                    },
                ]}
            >
                <View style={styles.cardContent}>
                    <View
                        style={[
                            styles.skeletonText,
                            { backgroundColor: theme.GRAY_LIGHT },
                        ]}
                    />
                    <View
                        style={[
                            styles.skeletonBadge,
                            { backgroundColor: theme.GRAY_LIGHT },
                        ]}
                    />
                </View>
            </View>
        ))}
    </View>
);

export default SkeletonLoader;
