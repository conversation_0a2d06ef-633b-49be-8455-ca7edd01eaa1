import React, { useRef, useEffect, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Easing,
    Image,
    Dimensions,
    ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';

const { height } = Dimensions.get('window');

const SiteSuccess = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();

    // Animation values
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, scaleAnim]);

    return (
        <View style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
            {/* Background Image & Gradient */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>
            <ScrollView
                contentContainerStyle={{ flexGrow: 1, minHeight: height }}
            >
                <BackButton
                    color={theme.WHITE}
                    onPress={() => router.push('/Home')}
                />
                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.card,
                            {
                                backgroundColor: theme.CARD,
                                shadowColor: theme.SHADOW,
                            },
                        ]}
                    >
                        <Animated.View
                            style={[
                                styles.iconWrapper,
                                { shadowColor: theme.PRIMARY },
                                {
                                    transform: [{ scale: scaleAnim }],
                                    opacity: fadeAnim,
                                },
                            ]}
                        >
                            <Ionicons
                                name="checkmark-circle"
                                size={90}
                                color={theme.PRIMARY}
                            />
                        </Animated.View>
                        <Animated.Text
                            style={[
                                styles.title,
                                { color: theme.PRIMARY, opacity: fadeAnim },
                            ]}
                        >
                            Site Registered Successfully!
                        </Animated.Text>
                        <Animated.Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY, opacity: fadeAnim },
                            ]}
                        >
                            Your property has been successfully registered on our platform.
                            You will receive notifications when potential buyers show interest.
                        </Animated.Text>

                        <Animated.View
                            style={[
                                styles.infoContainer,
                                { opacity: fadeAnim },
                            ]}
                        >
                            <View style={styles.infoItem}>
                                <Ionicons
                                    name="time-outline"
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                                <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
                                    Review process: 24-48 hours
                                </Text>
                            </View>
                            <View style={styles.infoItem}>
                                <Ionicons
                                    name="notifications-outline"
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                                <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
                                    You'll get notified about inquiries
                                </Text>
                            </View>
                            <View style={styles.infoItem}>
                                <Ionicons
                                    name="shield-checkmark-outline"
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                                <Text style={[styles.infoText, { color: theme.TEXT_PRIMARY }]}>
                                    Your documents are secure
                                </Text>
                            </View>
                        </Animated.View>

                        <Animated.View
                            style={[
                                styles.buttonContainer,
                                { opacity: fadeAnim },
                            ]}
                        >
                            <TouchableOpacity
                                style={[
                                    styles.button,
                                    styles.secondaryButton,
                                    { borderColor: theme.PRIMARY },
                                ]}
                                onPress={() => router.push('/Sites')}
                            >
                                <Text
                                    style={[
                                        styles.buttonText,
                                        styles.secondaryButtonText,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    View My Sites
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.button}
                                onPress={() => router.push('/Home')}
                            >
                                <LinearGradient
                                    colors={[theme.PRIMARY, theme.SECONDARY]}
                                    style={styles.buttonGradient}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                >
                                    <Text
                                        style={[
                                            styles.buttonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Back to Home
                                    </Text>
                                </LinearGradient>
                            </TouchableOpacity>
                        </Animated.View>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: height * 0.8,
        paddingHorizontal: 20,
    },
    card: {
        width: '100%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 32,
        alignItems: 'center',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 16,
        elevation: 8,
    },
    iconWrapper: {
        marginBottom: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 16,
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 32,
    },
    infoContainer: {
        width: '100%',
        marginBottom: 32,
    },
    infoItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    infoText: {
        fontSize: 16,
        marginLeft: 12,
        flex: 1,
    },
    buttonContainer: {
        width: '100%',
        gap: 12,
    },
    button: {
        borderRadius: 12,
        overflow: 'hidden',
    },
    secondaryButton: {
        borderWidth: 2,
        paddingVertical: 16,
        alignItems: 'center',
    },
    buttonGradient: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    secondaryButtonText: {
        fontWeight: '600',
    },
});

export default SiteSuccess;
