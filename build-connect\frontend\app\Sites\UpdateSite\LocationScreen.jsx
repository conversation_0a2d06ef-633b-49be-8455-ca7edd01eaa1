import React, { useContext } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import InputField from '../InputField';
import MapSelector from '../MapSelector';
import { FormContext, styles } from './SiteFormNavigator';

const LocationScreen = ({ navigation }) => {
  const { fields, setFields, region, setRegion, marker, setMarker, hasPermission, setHasPermission } = useContext(FormContext);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.h1}>Location and Site Map</Text>
      <View style={styles.card}>
        <Text style={styles.section}>Location Details</Text>
        <InputField
          label="Location"
          value={fields.location}
          onChangeText={(t) => setFields((f) => ({ ...f, location: t }))}
          placeholder="Enter Location"
        />
        <MapSelector
          region={region}
          setRegion={setRegion}
          marker={marker}
          setMarker={setMarker}
          fields={fields}
          setFields={setFields}
          hasPermission={hasPermission}
          setHasPermission={setHasPermission}
        />
        <InputField
          label="Latitude"
          value={fields.latitude}
          onChangeText={(t) => setFields((f) => ({ ...f, latitude: t }))}
          keyboardType="numeric"
          placeholder="Enter Latitude"
        />
        <InputField
          label="Longitude"
          value={fields.longitude}
          onChangeText={(t) => setFields((f) => ({ ...f, longitude: t }))}
          keyboardType="numeric"
          placeholder="Enter Longitude"
        />
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.8}
        >
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.nextButton}
          onPress={() => navigation.navigate('Encumbrance')}
          activeOpacity={0.8}
        >
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default LocationScreen;