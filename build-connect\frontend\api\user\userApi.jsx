import { privateAPIClient } from '../index';

export const fetchUserProfile = async () => {
    const response = await privateAPIClient.get(
        '/user-service/api/v1/user/profile'
    );
    return response.data.user;
};

export const updateUserProfile = async (data) => {
    // Check if data contains image data for file upload
    if (data.imageData) {
        const formData = new FormData();

        // Add text fields
        Object.keys(data).forEach((key) => {
            if (
                key !== 'imageData' &&
                data[key] !== null &&
                data[key] !== undefined
            ) {
                formData.append(key, data[key]);
            }
        });

        // Add image file if present
        if (data.imageData) {
            formData.append('avatar', {
                uri: data.imageData.uri,
                type: data.imageData.type,
                name: data.imageData.filename,
            });
        }

        const response = await privateAPIClient.put(
            '/user-service/api/v1/user/profile',
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                transformRequest: (formData) => formData,
            }
        );
        return response.data;
    } else {
        // Regular JSON update for text-only changes
        const response = await privateAPIClient.put(
            '/user-service/api/v1/user/profile',
            data
        );
        return response.data;
    }
};
