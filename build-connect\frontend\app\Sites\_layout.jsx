import { Stack } from 'expo-router';

export default function SitesLayout() {
    return (
        <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="index" />
            <Stack.Screen name="SiteForm" />
            <Stack.Screen name="SiteSuccess" />
            <Stack.Screen name="SiteList" />
            <Stack.Screen name="SiteDashboard" />
            {/* Legacy screens for backward compatibility */}
            <Stack.Screen name="AddSites" />
            <Stack.Screen name="UpdateSite" />
            <Stack.Screen name="SiteList" />
        </Stack>
    );
}
