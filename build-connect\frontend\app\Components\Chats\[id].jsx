import { useLocalSearchParams } from 'expo-router';
import { View, Text, Image, StyleSheet } from 'react-native';

export default function ChatScreen() {
    const { name, profile } = useLocalSearchParams();

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Image source={{ uri: profile }} style={styles.image} />
                <Text style={styles.name}>{name}</Text>
            </View>
            {/**/}
            <Text>Start chatting with {name}</Text>
        </View>
    );
}

export const unstable_settings = {
    initialRouteName: 'Chat',
};

export const options = {
    headerShown: true,
    tabBarStyle: { display: 'none' },
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    image: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginRight: 10,
    },
    name: {
        fontSize: 18,
        fontWeight: 'bold',
    },
});
