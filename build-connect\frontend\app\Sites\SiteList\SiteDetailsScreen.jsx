import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Modal,
} from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { useSiteDetails } from '../../../api/sites/getSiteById';

const SiteDetailsScreen = () => {
  const { siteId } = useLocalSearchParams();
  const { fields, isLoading } = useSiteDetails(siteId);
  const { width, height } = Dimensions.get('window');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const formatAddress = () => {
    const parts = [
      fields.addressLine1,
      fields.addressLine2,
      fields.district,
      fields.state,
      fields.pincode,
    ].filter(part => part && part.trim() !== '');
    return parts.length > 0 ? parts.join(', ') : 'Address not available';
  };

  const handleRetry = () => {
    Alert.alert('Retry', 'Please navigate back and re-enter to retry.');
  };

  const renderImageGallery = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        setSelectedImage(item);
        setModalVisible(true);
      }}
      accessibilityRole="imagebutton"
      accessibilityLabel={`View ${item.name || 'asset image'}`}
    >
      <Image
        source={{ uri: item.uri }}
        style={[styles.galleryImage, { width: width * 0.3, height: width * 0.3 }]}
        resizeMode="cover"
        onError={() => Alert.alert('Error', `Failed to load image: ${item.name}`)}
      />
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading Site Details...</Text>
      </View>
    );
  }

  // Combine all images for the gallery
  const allImages = [
    ...(fields.siteImages || []),
    ...(fields.encumbranceCert ? [fields.encumbranceCert] : []),
    ...(fields.propertyTaxRec ? [fields.propertyTaxRec] : []),
  ].filter(asset => asset.uri);

  return (
    <>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title} accessibilityRole="header">
          {fields.name || 'Unnamed Site'}
        </Text>

        {/* Site Thumbnail */}
        {fields.siteImages?.[0]?.uri ? (
          <Image
            source={{ uri: fields.siteImages[0].uri }}
            style={[styles.thumbnail, { height: width * 0.5 }]}
            resizeMode="cover"
            onError={() => Alert.alert('Error', 'Failed to load site thumbnail')}
            accessibilityLabel={`Thumbnail for ${fields.name || 'Unnamed Site'}`}
          />
        ) : (
          <View style={[styles.thumbnail, styles.placeholderImage, { height: width * 0.5 }]} />
        )}

        {/* Site Details */}
        <View style={styles.detailsContainer}>
          <Text style={styles.label}>Address:</Text>
          <Text style={styles.value}>{formatAddress()}</Text>
          <Text style={styles.label}>Plot Area:</Text>
          <Text style={styles.value}>{fields.plotArea ? `${fields.plotArea} sq.ft` : 'Not available'}</Text>
          <Text style={styles.label}>Price:</Text>
          <Text style={styles.value}>{fields.price ? `₹${fields.price}` : 'Not available'}</Text>
          <Text style={styles.label}>Landmark:</Text>
          <Text style={styles.value}>{fields.landmark || 'Not available'}</Text>
          <Text style={styles.label}>Location:</Text>
          <Text style={styles.value}>{fields.location || 'Not available'}</Text>
          <Text style={styles.label}>Coordinates:</Text>
          <Text style={styles.value}>
            {fields.latitude && fields.longitude
              ? `Lat: ${fields.latitude}, Long: ${fields.longitude}`
              : 'Not available'}
          </Text>
        </View>

        {/* Encumbrance Certificate */}
        {(fields.encOwnerName || fields.encDocumentNo || fields.surveyNo || fields.village || fields.subDistrict || fields.District) && (
          <View style={styles.detailsContainer}>
            <Text style={styles.sectionTitle}>Encumbrance Certificate</Text>
            <Text style={styles.label}>Document No:</Text>
            <Text style={styles.value}>{fields.encDocumentNo || 'Not available'}</Text>
            <Text style={styles.label}>Owner Name:</Text>
            <Text style={styles.value}>{fields.encOwnerName || 'Not available'}</Text>
            <Text style={styles.label}>Survey No:</Text>
            <Text style={styles.value}>{fields.surveyNo || 'Not available'}</Text>
            <Text style={styles.label}>Village:</Text>
            <Text style={styles.value}>{fields.village || 'Not available'}</Text>
            <Text style={styles.label}>Sub-District:</Text>
            <Text style={styles.value}>{fields.subDistrict || 'Not available'}</Text>
            <Text style={styles.label}>District:</Text>
            <Text style={styles.value}>{fields.District || 'Not available'}</Text>
          </View>
        )}

        {/* Property Tax Receipt */}
        {(fields.ptrOwnerName || fields.ptrReciptNo) && (
          <View style={styles.detailsContainer}>
            <Text style={styles.sectionTitle}>Property Tax Receipt</Text>
            <Text style={styles.label}>Receipt No:</Text>
            <Text style={styles.value}>{fields.ptrReciptNo || 'Not available'}</Text>
            <Text style={styles.label}>Owner Name:</Text>
            <Text style={styles.value}>{fields.ptrOwnerName || 'Not available'}</Text>
          </View>
        )}

        {/* Image Gallery */}
        {allImages.length > 0 && (
          <View style={styles.galleryContainer}>
            <Text style={styles.sectionTitle}>Images</Text>
            <FlatList
              data={allImages}
              renderItem={renderImageGallery}
              keyExtractor={(item, index) => item.name || `image-${index}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.galleryList}
            />
          </View>
        )}
      </ScrollView>

      {/* Full-Screen Image Preview Modal */}
      <Modal
        visible={modalVisible}
        transparent={false}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          {selectedImage?.uri && (
            <TouchableOpacity
              style={styles.modalImageContainer}
              onPress={() => setModalVisible(false)}
              accessibilityRole="imagebutton"
              accessibilityLabel={`Close preview of ${selectedImage.name || 'asset image'}`}
            >
              <Image
                source={{ uri: selectedImage.uri }}
                style={[styles.modalImage, { width: width * 0.9, height: height * 0.7 }]}
                resizeMode="contain"
                onError={() => {
                  Alert.alert('Error', `Failed to load image: ${selectedImage.name}`);
                  setModalVisible(false);
                }}
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setModalVisible(false)}
            accessibilityRole="button"
            accessibilityLabel="Close image preview"
          >
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    padding: 15,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
  },
  thumbnail: {
    width: '100%',
    borderRadius: 8,
    backgroundColor: '#eee',
    marginBottom: 15,
  },
  placeholderImage: {
    backgroundColor: '#ccc',
  },
  detailsContainer: {
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginTop: 10,
  },
  value: {
    fontSize: 14,
    color: '#555',
    marginBottom: 5,
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    textAlign: 'center',
    marginBottom: 10,
  },
  loadingText: {
    fontSize: 16,
    color: '#0000ff',
    marginTop: 10,
  },
  retryButton: {
    backgroundColor: '#0000ff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginTop: 10,
  },
  retryButtonText: {
    color: '#fff',
  },
  galleryContainer: {
    marginBottom: 15,
  },
  galleryList: {
    paddingVertical: 10,
  },
  galleryImage: {
    borderRadius: 8,
    marginRight: 10,
    backgroundColor: '#eee',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImage: {
    borderRadius: 8,
  },
  closeButton: {
    backgroundColor: '#0000ff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginBottom: 20,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SiteDetailsScreen;