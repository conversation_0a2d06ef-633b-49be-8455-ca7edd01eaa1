import React, { useContext } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import InputField from '../InputField';
import FilePicker from '../FilePicker';
import { FormContext, styles } from './SiteFormNavigator';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const EncumbranceScreen = ({ navigation }) => {
  const { fields, setFields } = useContext(FormContext);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.h1}>Encumbrance Certificate</Text>
      <View style={styles.card}>
        <Text style={styles.section}>Encumbrance Details</Text>
        <InputField
          label="Owner name"
          value={fields.encOwnerName}
          onChangeText={(t) => setFields((f) => ({ ...f, encOwnerName: t }))}
          placeholder="Enter Owner name"
        />
        <InputField
          label="Document No."
          value={fields.encDocumentNo}
          onChangeText={(t) => setFields((f) => ({ ...f, encDocumentNo: t }))}
          placeholder="Enter Document No."
        />
        <InputField
          label="Survey No."
          value={fields.surveyNo}
          onChangeText={(t) => setFields((f) => ({ ...f, surveyNo: t }))}
          placeholder="Enter Survey No."
        />
        <InputField
          label="Village"
          value={fields.village}
          onChangeText={(t) => setFields((f) => ({ ...f, village: t }))}
          placeholder="Enter Village"
        />
        <InputField
          label="Sub-District"
          value={fields.subDistrict}
          onChangeText={(t) => setFields((f) => ({ ...f, subDistrict: t }))}
          placeholder="Enter Sub-District"
        />
        <InputField
          label="District"
          value={fields.District}
          onChangeText={(t) => setFields((f) => ({ ...f, District: t }))}
          placeholder="Enter District"
        />
        <FilePicker
          label="Pick encumbrance certificate"
          files={fields.encumbranceCert}
          setFiles={setFields}
          keyName="encumbranceCert"
          allowedTypes={ALLOWED_TYPES}
          maxFileSize={MAX_FILE_SIZE}
        />
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.8}
        >
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.nextButton}
          onPress={() => navigation.navigate('PropertyTax')}
          activeOpacity={0.8}
        >
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default EncumbranceScreen;