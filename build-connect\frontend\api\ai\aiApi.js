import axios from 'axios';
import { privateAPIClient } from '../index';

const aiApi = axios.create({
    baseURL: `${privateAPIClient.defaults.baseURL}/api/ai`,
});

// Add auth token to requests
aiApi.interceptors.request.use(async (config) => {
    try {
        // Use AsyncStorage instead of localStorage for React Native
        const AsyncStorage =
            require('@react-native-async-storage/async-storage').default;
        const token = await AsyncStorage.getItem('authToken');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    } catch (error) {
        // Handle errors
    }
    return config;
});

// Document Validation
export const validateLandDocuments = async (landId, documents) => {
    const formData = new FormData();
    formData.append('landId', landId);

    documents.forEach((doc, index) => {
        formData.append(`documents[${index}]`, {
            uri: doc.uri,
            type: doc.type,
            name: doc.name,
        });
        formData.append(`documentTypes[${index}]`, doc.documentType);
    });

    const { data } = await aiApi.post('/documents/validate', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

export const getDocumentValidationResults = async (validationId) => {
    const { data } = await aiApi.get(`/documents/validation/${validationId}`);
    return data;
};

export const revalidateDocuments = async (validationId, correctedDocuments) => {
    const { data } = await aiApi.post(`/documents/revalidate/${validationId}`, {
        correctedDocuments,
    });
    return data;
};

export const getDocumentValidationHistory = async (landId) => {
    const { data } = await aiApi.get(`/documents/validation-history/${landId}`);
    return data;
};

// Property Valuation
export const getPropertyValuation = async (propertyData) => {
    const { data } = await aiApi.post('/valuation/property', propertyData);
    return data;
};

export const getComparativeMarketAnalysis = async (propertyId, radius = 5) => {
    const { data } = await aiApi.get(`/valuation/cma/${propertyId}`, {
        params: { radius },
    });
    return data;
};

export const getPriceHistory = async (propertyId) => {
    const { data } = await aiApi.get(`/valuation/price-history/${propertyId}`);
    return data;
};

export const getPricePrediction = async (propertyId, timeframe = '1y') => {
    const { data } = await aiApi.get(
        `/valuation/price-prediction/${propertyId}`,
        {
            params: { timeframe },
        }
    );
    return data;
};

// Contractor Recommendations
export const getContractorRecommendations = async (projectData) => {
    const { data } = await aiApi.post(
        '/recommendations/contractors',
        projectData
    );
    return data;
};

export const getPersonalizedContractorSuggestions = async (
    userId,
    preferences = {}
) => {
    const { data } = await aiApi.post(
        '/recommendations/personalized-contractors',
        {
            userId,
            preferences,
        }
    );
    return data;
};

export const getContractorMatchScore = async (
    contractorId,
    projectRequirements
) => {
    const { data } = await aiApi.post('/recommendations/contractor-match', {
        contractorId,
        projectRequirements,
    });
    return data;
};

export const updateRecommendationFeedback = async (
    recommendationId,
    feedback
) => {
    const { data } = await aiApi.post(
        `/recommendations/feedback/${recommendationId}`,
        {
            feedback, // 'helpful', 'not_helpful', 'hired', 'contacted'
        }
    );
    return data;
};

// Property Recommendations
export const getPropertyRecommendations = async (userId, preferences = {}) => {
    const { data } = await aiApi.post('/recommendations/properties', {
        userId,
        preferences,
    });
    return data;
};

export const getSimilarProperties = async (propertyId, limit = 10) => {
    const { data } = await aiApi.get(
        `/recommendations/similar-properties/${propertyId}`,
        {
            params: { limit },
        }
    );
    return data;
};

export const getInvestmentOpportunities = async (userId, investmentProfile) => {
    const { data } = await aiApi.post(
        '/recommendations/investment-opportunities',
        {
            userId,
            investmentProfile,
        }
    );
    return data;
};

// Fraud Detection
export const analyzeListingForFraud = async (listingData) => {
    const { data } = await aiApi.post(
        '/fraud-detection/analyze-listing',
        listingData
    );
    return data;
};

export const analyzeUserBehavior = async (userId, behaviorData) => {
    const { data } = await aiApi.post('/fraud-detection/analyze-behavior', {
        userId,
        behaviorData,
    });
    return data;
};

export const reportSuspiciousActivity = async (activityData) => {
    const { data } = await aiApi.post(
        '/fraud-detection/report-suspicious',
        activityData
    );
    return data;
};

export const getFraudRiskScore = async (transactionData) => {
    const { data } = await aiApi.post(
        '/fraud-detection/risk-score',
        transactionData
    );
    return data;
};

// Market Analysis
export const getMarketTrends = async (location, timeframe = '1y') => {
    const { data } = await aiApi.post('/market-analysis/trends', {
        location,
        timeframe,
    });
    return data;
};

export const getAreaInsights = async (latitude, longitude, radius = 5) => {
    const { data } = await aiApi.get('/market-analysis/area-insights', {
        params: { latitude, longitude, radius },
    });
    return data;
};

export const getDemandForecast = async (location, propertyType) => {
    const { data } = await aiApi.post('/market-analysis/demand-forecast', {
        location,
        propertyType,
    });
    return data;
};

export const getInvestmentAnalysis = async (propertyId) => {
    const { data } = await aiApi.get(
        `/market-analysis/investment/${propertyId}`
    );
    return data;
};

// Smart Search
export const getSmartSearchSuggestions = async (query, context = {}) => {
    const { data } = await aiApi.post('/search/suggestions', {
        query,
        context,
    });
    return data;
};

export const performSemanticSearch = async (query, filters = {}) => {
    const { data } = await aiApi.post('/search/semantic', {
        query,
        filters,
    });
    return data;
};

export const getSearchInsights = async (searchHistory) => {
    const { data } = await aiApi.post('/search/insights', {
        searchHistory,
    });
    return data;
};

// Chatbot and Virtual Assistant
export const sendChatMessage = async (
    message,
    conversationId = null,
    context = {}
) => {
    const { data } = await aiApi.post('/chat/message', {
        message,
        conversationId,
        context,
    });
    return data;
};

export const getChatHistory = async (conversationId) => {
    const { data } = await aiApi.get(`/chat/history/${conversationId}`);
    return data;
};

export const startNewConversation = async (topic = 'general') => {
    const { data } = await aiApi.post('/chat/new-conversation', {
        topic,
    });
    return data;
};

export const endConversation = async (conversationId, feedback = null) => {
    const { data } = await aiApi.post(
        `/chat/end-conversation/${conversationId}`,
        {
            feedback,
        }
    );
    return data;
};

// Image Analysis
export const analyzePropertyImages = async (images, propertyType) => {
    const formData = new FormData();
    formData.append('propertyType', propertyType);

    images.forEach((image, index) => {
        formData.append(`images[${index}]`, {
            uri: image.uri,
            type: image.type,
            name: image.name,
        });
    });

    const { data } = await aiApi.post('/image-analysis/property', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

export const detectPropertyFeatures = async (imageUri) => {
    const formData = new FormData();
    formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'property_image.jpg',
    });

    const { data } = await aiApi.post('/image-analysis/features', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

export const estimatePropertyCondition = async (images) => {
    const formData = new FormData();

    images.forEach((image, index) => {
        formData.append(`images[${index}]`, {
            uri: image.uri,
            type: image.type,
            name: image.name,
        });
    });

    const { data } = await aiApi.post('/image-analysis/condition', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

// Predictive Analytics
export const getPriceMovementPrediction = async (
    propertyId,
    timeframe = '6m'
) => {
    const { data } = await aiApi.get(
        `/analytics/price-movement/${propertyId}`,
        {
            params: { timeframe },
        }
    );
    return data;
};

export const getMarketVolatilityAnalysis = async (location) => {
    const { data } = await aiApi.post('/analytics/market-volatility', {
        location,
    });
    return data;
};

export const getBestTimeToSell = async (propertyId) => {
    const { data } = await aiApi.get(
        `/analytics/best-time-to-sell/${propertyId}`
    );
    return data;
};

export const getSeasonalTrends = async (location, propertyType) => {
    const { data } = await aiApi.post('/analytics/seasonal-trends', {
        location,
        propertyType,
    });
    return data;
};

// User Behavior Analysis
export const analyzeUserPreferences = async (userId) => {
    const { data } = await aiApi.get(`/user-analysis/preferences/${userId}`);
    return data;
};

export const getUserInsights = async (userId) => {
    const { data } = await aiApi.get(`/user-analysis/insights/${userId}`);
    return data;
};

export const predictUserActions = async (userId, context = {}) => {
    const { data } = await aiApi.post(
        `/user-analysis/predict-actions/${userId}`,
        {
            context,
        }
    );
    return data;
};

export const getPersonalizationData = async (userId) => {
    const { data } = await aiApi.get(
        `/user-analysis/personalization/${userId}`
    );
    return data;
};

// AI Model Management
export const getModelPerformance = async (modelType) => {
    const { data } = await aiApi.get(`/models/performance/${modelType}`);
    return data;
};

export const updateModelFeedback = async (
    modelType,
    predictionId,
    actualOutcome
) => {
    const { data } = await aiApi.post(`/models/feedback/${modelType}`, {
        predictionId,
        actualOutcome,
    });
    return data;
};

export const getModelVersions = async (modelType) => {
    const { data } = await aiApi.get(`/models/versions/${modelType}`);
    return data;
};

// AI Configuration
export const getAISettings = async () => {
    const { data } = await aiApi.get('/settings');
    return data;
};

export const updateAISettings = async (settings) => {
    const { data } = await aiApi.put('/settings', settings);
    return data;
};

export const enableAIFeature = async (featureName, enabled) => {
    const { data } = await aiApi.put(`/settings/feature/${featureName}`, {
        enabled,
    });
    return data;
};

export default aiApi;
