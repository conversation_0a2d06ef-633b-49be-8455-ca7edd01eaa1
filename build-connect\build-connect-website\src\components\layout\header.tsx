'use client'

import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface HeaderProps {
  className?: string
}

export function Header({ className }: HeaderProps) {
  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60",
      className
    )}>
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-lg">BC</span>
          </div>
          <span className="font-bold text-xl text-primary">BUILD-CONNECT</span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          <Link 
            href="/properties" 
            className="text-sm font-medium text-foreground/80 hover:text-foreground transition-colors"
          >
            Properties
          </Link>
          <Link 
            href="/brokers" 
            className="text-sm font-medium text-foreground/80 hover:text-foreground transition-colors"
          >
            Site Scouts
          </Link>
          <Link 
            href="/contractors" 
            className="text-sm font-medium text-foreground/80 hover:text-foreground transition-colors"
          >
            Contractors
          </Link>
          <Link 
            href="/map" 
            className="text-sm font-medium text-foreground/80 hover:text-foreground transition-colors"
          >
            Map Explorer
          </Link>
        </nav>

        {/* Auth Buttons */}
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/auth/login">Login</Link>
          </Button>
          <Button size="sm" asChild>
            <Link href="/auth/register">Sign Up</Link>
          </Button>
        </div>
      </div>
    </header>
  )
}
