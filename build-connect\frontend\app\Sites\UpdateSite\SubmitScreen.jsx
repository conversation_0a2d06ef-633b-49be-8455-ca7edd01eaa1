import React, { useState, useContext } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../../api';
import { FormContext, styles } from './SiteFormNavigator';

const buildPart = (asset) => ({
  uri: asset.uri,
  name: asset.name || `file_${Date.now()}`,
  type: asset.mimeType || 'application/octet-stream',
});

const SubmitScreen = ({ navigation }) => {
  const { fields, setFields, region, setRegion, setMarker, setHasPermission, siteId } = useContext(FormContext);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSave = async () => {
    // Validate required fields
    if (!fields.name || !fields.pincode || !fields.plotArea) {
      Alert.alert('Missing fields', 'Name, pincode, and plot area are required.');
      return;
    }

    // Validate numeric fields
    if (fields.plotArea && isNaN(parseFloat(fields.plotArea))) {
      Alert.alert('Invalid input', 'Plot area must be a valid number.');
      return;
    }
    if (fields.price && isNaN(parseFloat(fields.price))) {
      Alert.alert('Invalid input', 'Price must be a valid number.');
      return;
    }
    if (fields.latitude && isNaN(parseFloat(fields.latitude))) {
      Alert.alert('Invalid input', 'Latitude must be a valid number.');
      return;
    }
    if (fields.longitude && isNaN(parseFloat(fields.longitude))) {
      Alert.alert('Invalid input', 'Longitude must be a valid number.');
      return;
    }

    const form = new FormData();

    // Append siteImages if they exist and are valid
    if (Array.isArray(fields.siteImages) && fields.siteImages.length > 0) {
      fields.siteImages.forEach((image, index) => {
        if (image.uri && image.mimeType && image.mimeType !== 'existing') { // Skip existing files
          form.append('siteImages', buildPart(image));
        }
      });
    }

    // Append encumbranceCert if it exists and is not an existing file
    if (fields.encumbranceCert?.uri && fields.encumbranceCert.mimeType !== 'existing') {
      form.append('encumbranceCertificate', buildPart(fields.encumbranceCert));
    }

    // Append propertyTaxRec if it exists and is not an existing file
    if (fields.propertyTaxRec?.uri && fields.propertyTaxRec.mimeType !== 'existing') {
      form.append('propertyTaxReceipt', buildPart(fields.propertyTaxRec));
    }

    // Append text fields, converting numbers to strings and handling null/undefined
    Object.entries(fields).forEach(([key, value]) => {
      if (key !== 'siteImages' && key !== 'encumbranceCert' && key !== 'propertyTaxRec') {
        form.append(key, String(value ?? ''));
      }
    });

    try {
      setLoading(true);
      const response = await privateAPIClient.patch(`/site-service/api/v1/sites/${siteId}`, form, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      Alert.alert('Success', 'Site updated successfully!', [
        { text: 'OK', onPress: () => router.replace('/Sites') },
      ]);

      // Reset form to initial state
      setFields({
        name: '',
        addressLine1: '',
        addressLine2: '',
        landmark: '',
        location: '',
        pincode: '',
        state: '',
        district: '',
        plotArea: '',
        price: '',
        latitude: '',
        longitude: '',
        encOwnerName: '',
        encDocumentNo: '',
        surveyNo: '',
        village: '',
        subDistrict: '',
        District: '',
        ptrOwnerName: '',
        ptrReciptNo: '',
        siteImages: [],
        encumbranceCert: null,
        propertyTaxRec: null,
      });
      setRegion({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      });
      setMarker(null);
      setHasPermission(false);
    } catch (err) {
      console.error('Update failed:', {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
      });
      Alert.alert('Update failed', `Failed to update site: ${err.message}. Check backend logs for details.`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.h1}>Review and Update</Text>
      <View style={styles.card}>
        <Text style={styles.section}>Confirm Details</Text>
        <Text style={styles.reviewText}>Please review all details before updating.</Text>
        <Text style={styles.reviewText}>Site Name: {fields.name || 'Not provided'}</Text>
        <Text style={styles.reviewText}>Pincode: {fields.pincode || 'Not provided'}</Text>
        <Text style={styles.reviewText}>Plot Area: {fields.plotArea || 'Not provided'}</Text>
        <Text style={styles.reviewText}>Property Tax Receipt: {fields.propertyTaxRec ? 'Uploaded' : 'Not uploaded'}</Text>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.8}
        >
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
          activeOpacity={0.8}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Updating…' : 'Update Site'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SubmitScreen;