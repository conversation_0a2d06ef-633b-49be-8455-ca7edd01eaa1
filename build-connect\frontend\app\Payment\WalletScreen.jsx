import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    Alert,
    Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';

const { width } = Dimensions.get('window');

const WalletScreen = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [balance, setBalance] = useState(15750.5);
    const [selectedTab, setSelectedTab] = useState('transactions');

    // Mock transaction data
    const transactions = [
        {
            id: 1,
            type: 'credit',
            amount: 5000,
            description: 'Payment received from <PERSON>',
            date: '2024-01-15',
            status: 'completed',
            category: 'payment',
        },
        {
            id: 2,
            type: 'debit',
            amount: 2500,
            description: 'Payment to Elite Builders',
            date: '2024-01-14',
            status: 'completed',
            category: 'contractor',
        },
        {
            id: 3,
            type: 'credit',
            amount: 1000,
            description: 'Wallet top-up',
            date: '2024-01-13',
            status: 'completed',
            category: 'topup',
        },
        {
            id: 4,
            type: 'debit',
            amount: 750,
            description: 'Platform fee',
            date: '2024-01-12',
            status: 'completed',
            category: 'fee',
        },
    ];

    const quickActions = [
        {
            id: 'add-money',
            title: 'Add Money',
            icon: 'add-circle',
            color: '#4CAF50',
            onPress: () => handleAddMoney(),
        },
        {
            id: 'send-money',
            title: 'Send Money',
            icon: 'send',
            color: '#2196F3',
            onPress: () => handleSendMoney(),
        },
        {
            id: 'withdraw',
            title: 'Withdraw',
            icon: 'arrow-down-circle',
            color: '#FF9800',
            onPress: () => handleWithdraw(),
        },
        {
            id: 'history',
            title: 'History',
            icon: 'time',
            color: '#9C27B0',
            onPress: () => setSelectedTab('history'),
        },
    ];

    const handleAddMoney = () => {
        Alert.alert('Add Money', 'Choose amount to add to your wallet', [
            { text: 'Cancel', style: 'cancel' },
            { text: '₹1,000', onPress: () => addMoney(1000) },
            { text: '₹5,000', onPress: () => addMoney(5000) },
            { text: '₹10,000', onPress: () => addMoney(10000) },
        ]);
    };

    const addMoney = (amount) => {
        setBalance((prev) => prev + amount);
        Alert.alert('Success', `₹${amount} added to your wallet successfully!`);
    };

    const handleSendMoney = () => {
        router.push('/Payment/SendMoney');
    };

    const handleWithdraw = () => {
        router.push('/Payment/WithdrawMoney');
    };

    const getTransactionIcon = (category, type) => {
        const iconMap = {
            payment:
                type === 'credit' ? 'arrow-down-circle' : 'arrow-up-circle',
            contractor: 'hammer',
            topup: 'add-circle',
            fee: 'card',
        };
        return iconMap[category] || 'swap-horizontal';
    };

    const getTransactionColor = (type) => {
        return type === 'credit' ? '#4CAF50' : '#F44336';
    };

    const renderTransaction = (transaction) => (
        <TouchableOpacity
            key={transaction.id}
            style={[styles.transactionCard, { backgroundColor: theme.CARD }]}
            onPress={() =>
                router.push(`/Payment/TransactionDetails?id=${transaction.id}`)
            }
        >
            <View style={styles.transactionLeft}>
                <View
                    style={[
                        styles.transactionIcon,
                        {
                            backgroundColor:
                                getTransactionColor(transaction.type) + '20',
                        },
                    ]}
                >
                    <Ionicons
                        name={getTransactionIcon(
                            transaction.category,
                            transaction.type
                        )}
                        size={24}
                        color={getTransactionColor(transaction.type)}
                    />
                </View>
                <View style={styles.transactionInfo}>
                    <Text
                        style={[
                            styles.transactionDescription,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        {transaction.description}
                    </Text>
                    <Text
                        style={[
                            styles.transactionDate,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {new Date(transaction.date).toLocaleDateString()}
                    </Text>
                </View>
            </View>
            <View style={styles.transactionRight}>
                <Text
                    style={[
                        styles.transactionAmount,
                        { color: getTransactionColor(transaction.type) },
                    ]}
                >
                    {transaction.type === 'credit' ? '+' : '-'}₹
                    {transaction.amount.toLocaleString()}
                </Text>
                <Text
                    style={[
                        styles.transactionStatus,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {transaction.status}
                </Text>
            </View>
        </TouchableOpacity>
    );

    const renderQuickAction = (action) => (
        <TouchableOpacity
            key={action.id}
            style={[styles.quickActionCard, { backgroundColor: theme.CARD }]}
            onPress={action.onPress}
        >
            <View
                style={[
                    styles.quickActionIcon,
                    { backgroundColor: action.color + '20' },
                ]}
            >
                <Ionicons name={action.icon} size={28} color={action.color} />
            </View>
            <Text
                style={[styles.quickActionTitle, { color: theme.TEXT_PRIMARY }]}
            >
                {action.title}
            </Text>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>My Wallet</Text>
                    <TouchableOpacity
                        onPress={() => router.push('/Payment/WalletSettings')}
                    >
                        <Ionicons name="settings" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
            >
                {/* Balance Card */}
                <LinearGradient
                    colors={['#667eea', '#764ba2']}
                    style={styles.balanceCard}
                >
                    <View style={styles.balanceHeader}>
                        <Text style={styles.balanceLabel}>
                            Available Balance
                        </Text>
                        <Ionicons name="wallet" size={24} color="#fff" />
                    </View>
                    <Text style={styles.balanceAmount}>
                        ₹{balance.toLocaleString()}
                    </Text>
                    <Text style={styles.balanceSubtext}>
                        Last updated: Just now
                    </Text>
                </LinearGradient>

                {/* Quick Actions */}
                <View style={styles.section}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Quick Actions
                    </Text>
                    <View style={styles.quickActionsGrid}>
                        {quickActions.map(renderQuickAction)}
                    </View>
                </View>

                {/* Tabs */}
                <View style={styles.tabsContainer}>
                    <TouchableOpacity
                        style={[
                            styles.tab,
                            selectedTab === 'transactions' && {
                                backgroundColor: theme.PRIMARY,
                            },
                        ]}
                        onPress={() => setSelectedTab('transactions')}
                    >
                        <Text
                            style={[
                                styles.tabText,
                                {
                                    color:
                                        selectedTab === 'transactions'
                                            ? '#fff'
                                            : theme.TEXT_SECONDARY,
                                },
                            ]}
                        >
                            Recent Transactions
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[
                            styles.tab,
                            selectedTab === 'history' && {
                                backgroundColor: theme.PRIMARY,
                            },
                        ]}
                        onPress={() => setSelectedTab('history')}
                    >
                        <Text
                            style={[
                                styles.tabText,
                                {
                                    color:
                                        selectedTab === 'history'
                                            ? '#fff'
                                            : theme.TEXT_SECONDARY,
                                },
                            ]}
                        >
                            Full History
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Transactions */}
                <View style={styles.section}>
                    {transactions.map(renderTransaction)}
                </View>

                <View style={{ height: 100 }} />
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 20,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    content: {
        flex: 1,
    },
    balanceCard: {
        margin: 20,
        padding: 24,
        borderRadius: 16,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
    },
    balanceHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    balanceLabel: {
        fontSize: 16,
        color: 'rgba(255,255,255,0.8)',
    },
    balanceAmount: {
        fontSize: 32,
        fontWeight: 'bold',
        color: '#fff',
        marginBottom: 4,
    },
    balanceSubtext: {
        fontSize: 14,
        color: 'rgba(255,255,255,0.7)',
    },
    section: {
        paddingHorizontal: 20,
        marginBottom: 20,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    quickActionsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    quickActionCard: {
        width: (width - 60) / 2,
        padding: 16,
        borderRadius: 12,
        alignItems: 'center',
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    quickActionIcon: {
        width: 56,
        height: 56,
        borderRadius: 28,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    quickActionTitle: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
    },
    tabsContainer: {
        flexDirection: 'row',
        marginHorizontal: 20,
        marginBottom: 20,
        backgroundColor: 'rgba(0,0,0,0.05)',
        borderRadius: 8,
        padding: 4,
    },
    tab: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 6,
        alignItems: 'center',
    },
    tabText: {
        fontSize: 14,
        fontWeight: '600',
    },
    transactionCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    transactionLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    transactionIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    transactionInfo: {
        flex: 1,
    },
    transactionDescription: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    transactionDate: {
        fontSize: 14,
    },
    transactionRight: {
        alignItems: 'flex-end',
    },
    transactionAmount: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    transactionStatus: {
        fontSize: 12,
        textTransform: 'capitalize',
    },
});

export default WalletScreen;
