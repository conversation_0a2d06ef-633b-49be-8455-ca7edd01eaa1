'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function ChatsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground">Messages</h1>
        <p className="mt-2 text-muted-foreground">
          Your conversations with brokers, contractors, and other users
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Chat List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Conversations</h3>
            <Button size="sm">New Chat</Button>
          </div>
          
          <Input placeholder="Search conversations..." />
          
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <Card key={i} className="cursor-pointer transition-all hover:shadow-card-hover">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                      <span className="text-sm">👤</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">Rajesh Kumar</p>
                      <p className="text-sm text-muted-foreground truncate">
                        Thanks for your interest in the property...
                      </p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      2h ago
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Chat Window */}
        <div className="lg:col-span-2">
          <Card className="h-[600px] flex flex-col">
            <CardHeader className="border-b">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                  <span className="text-sm">👤</span>
                </div>
                <div>
                  <CardTitle className="text-lg">Rajesh Kumar</CardTitle>
                  <CardDescription>Site Scout • Online</CardDescription>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                {/* Sample Messages */}
                <div className="flex justify-start">
                  <div className="max-w-xs bg-muted rounded-lg p-3">
                    <p className="text-sm">Hello! I saw your interest in the Whitefield property. Would you like to schedule a visit?</p>
                    <p className="text-xs text-muted-foreground mt-1">10:30 AM</p>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <div className="max-w-xs bg-primary text-primary-foreground rounded-lg p-3">
                    <p className="text-sm">Yes, I&apos;m interested. When would be a good time?</p>
                    <p className="text-xs opacity-80 mt-1">10:32 AM</p>
                  </div>
                </div>
                
                <div className="flex justify-start">
                  <div className="max-w-xs bg-muted rounded-lg p-3">
                    <p className="text-sm">How about tomorrow at 2 PM? I can show you around the property and nearby amenities.</p>
                    <p className="text-xs text-muted-foreground mt-1">10:35 AM</p>
                  </div>
                </div>
              </div>
            </CardContent>
            
            <div className="border-t p-4">
              <div className="flex space-x-2">
                <Input placeholder="Type your message..." className="flex-1" />
                <Button size="sm">📎</Button>
                <Button size="sm">Send</Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
