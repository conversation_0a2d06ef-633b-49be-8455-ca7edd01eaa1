import axios from 'axios';
import { privateAPIClient } from '../index';

const landApi = axios.create({
    baseURL: `${privateAPIClient.defaults.baseURL}/api/land`,
});

// Add auth token to requests
landApi.interceptors.request.use((config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Land listing and browsing
export const fetchLands = async (params = {}) => {
    const { data } = await landApi.get('/list', { params });
    return data;
};

export const fetchLandById = async (landId) => {
    const { data } = await landApi.get(`/${landId}`);
    return data;
};

export const fetchNearbyLands = async (location, radius = 10) => {
    const { data } = await landApi.get('/nearby', {
        params: {
            latitude: location.latitude,
            longitude: location.longitude,
            radius,
        },
    });
    return data;
};

// Land selling workflow
export const createLandListing = async (landData) => {
    const formData = new FormData();

    // Add basic land information
    Object.keys(landData).forEach((key) => {
        if (key === 'documents' || key === 'images') {
            // Handle file uploads
            landData[key].forEach((file, index) => {
                formData.append(`${key}[${index}]`, file);
            });
        } else if (typeof landData[key] === 'object') {
            formData.append(key, JSON.stringify(landData[key]));
        } else {
            formData.append(key, landData[key]);
        }
    });

    const { data } = await landApi.post('/create', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

export const updateLandListing = async (landId, landData) => {
    const formData = new FormData();

    Object.keys(landData).forEach((key) => {
        if (key === 'documents' || key === 'images') {
            landData[key].forEach((file, index) => {
                formData.append(`${key}[${index}]`, file);
            });
        } else if (typeof landData[key] === 'object') {
            formData.append(key, JSON.stringify(landData[key]));
        } else {
            formData.append(key, landData[key]);
        }
    });

    const { data } = await landApi.put(`/${landId}`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
    return data;
};

export const deleteLandListing = async (landId) => {
    const { data } = await landApi.delete(`/${landId}`);
    return data;
};

// Land buying with broker integration
export const expressInterest = async (landId, message = '') => {
    const { data } = await landApi.post(`/${landId}/interest`, { message });
    return data;
};

export const scheduleSiteVisit = async (landId, visitData) => {
    const { data } = await landApi.post(`/${landId}/schedule-visit`, visitData);
    return data;
};

// Land verification and status
export const getLandVerificationStatus = async (landId) => {
    const { data } = await landApi.get(`/${landId}/verification-status`);
    return data;
};

export const submitLandForVerification = async (landId) => {
    const { data } = await landApi.post(`/${landId}/submit-verification`);
    return data;
};

// AI validation
export const getAIValidationResults = async (landId) => {
    const { data } = await landApi.get(`/${landId}/ai-validation`);
    return data;
};

// Land categories and filters
export const getLandCategories = async () => {
    const { data } = await landApi.get('/categories');
    return data;
};

export const searchLands = async (searchParams) => {
    const { data } = await landApi.get('/search', { params: searchParams });
    return data;
};

// User's land listings
export const getUserLands = async (userId) => {
    const { data } = await landApi.get(`/user/${userId}`);
    return data;
};

// Land analytics for sellers
export const getLandAnalytics = async (landId) => {
    const { data } = await landApi.get(`/${landId}/analytics`);
    return data;
};

export default landApi;
