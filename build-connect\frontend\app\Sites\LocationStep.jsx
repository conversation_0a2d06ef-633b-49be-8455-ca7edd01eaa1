import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';
import MapSelector from './MapSelector';

const LocationStep = ({ theme, formik, setStep, isSubmitting }) => {
    const { values, errors, touched, handleChange, handleBlur, setFieldValue, validateForm, setFieldTouched } = formik;
    const [region, setRegion] = useState({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
    });
    const [marker, setMarker] = useState(null);
    const [hasPermission, setHasPermission] = useState(false);

    const handleLocationChange = ({ latitude, longitude }) => {
        setFieldValue('latitude', latitude);
        setFieldValue('longitude', longitude);
    };

    const handleNext = async () => {
        const errors = await validateForm();
        const fields = [
            'location',
            'latitude',
            'longitude',
            'village',
            'surveyNumber',
        ];
        if (!fields.some((field) => errors[field])) {
            setStep('encumbrance');
        } else {
            fields.forEach((field) =>
                setFieldTouched(field, true)
            );
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Location Details
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
                Provide location information and mark your property on the map
            </Text>

            {/* Location */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.location && touched.location
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.location}
                    onChangeText={handleChange('location')}
                    onBlur={handleBlur('location')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter location"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Location"
                />
            </View>
            {errors.location && touched.location && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.location}
                </Text>
            )}

            {/* Map Selector */}
            <View style={styles.mapContainer}>
                <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>
                    Select Location on Map
                </Text>
                <MapSelector
                    region={region}
                    setRegion={setRegion}
                    marker={marker}
                    setMarker={setMarker}
                    fields={values}
                    setFields={handleLocationChange}
                    hasPermission={hasPermission}
                    setHasPermission={setHasPermission}
                    theme={theme}
                />
            </View>

            {/* Village */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.village && touched.village
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="home-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.village}
                    onChangeText={handleChange('village')}
                    onBlur={handleBlur('village')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter village"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Village"
                />
            </View>
            {errors.village && touched.village && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.village}
                </Text>
            )}

            {/* Survey Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.surveyNumber && touched.surveyNumber
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="document-text-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.surveyNumber}
                    onChangeText={handleChange('surveyNumber')}
                    onBlur={handleBlur('surveyNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter survey number"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Survey number"
                />
            </View>
            {errors.surveyNumber && touched.surveyNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.surveyNumber}
                </Text>
            )}

            {/* Latitude (Hidden from user but shown for debugging) */}
            <View
                style={[
                    styles.inputContainer,
                    styles.disabledInputContainer,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="navigate-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.latitude}
                    style={[
                        styles.input,
                        styles.disabledInput,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                    editable={false}
                    placeholder="Latitude (auto-filled)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Latitude"
                />
            </View>

            {/* Longitude (Hidden from user but shown for debugging) */}
            <View
                style={[
                    styles.inputContainer,
                    styles.disabledInputContainer,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="navigate-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.longitude}
                    style={[
                        styles.input,
                        styles.disabledInput,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                    editable={false}
                    placeholder="Longitude (auto-filled)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Longitude"
                />
            </View>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.backButton, { borderColor: theme.INPUT_BORDER }]}
                    onPress={() => setStep('siteDetails')}
                    disabled={isSubmitting}
                    accessibilityLabel="Go back to site details"
                    accessibilityRole="button"
                >
                    <Text style={[styles.backButtonText, { color: theme.PRIMARY }]}>
                        Back
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleNext}
                    disabled={isSubmitting}
                    accessibilityLabel="Proceed to encumbrance details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default LocationStep;
