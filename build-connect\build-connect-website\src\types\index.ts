// User types
export interface User {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  role: UserRole
  location?: Location
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'buyer' | 'broker' | 'contractor' | 'admin'

export interface Location {
  latitude: number
  longitude: number
  address: string
  city: string
  state: string
  pincode: string
}

// Property types
export interface Property {
  id: string
  title: string
  description: string
  type: PropertyType
  price: number
  area: number
  location: Location
  images: PropertyImage[]
  documents: PropertyDocument[]
  amenities: string[]
  status: PropertyStatus
  brokerId?: string
  broker?: Broker
  sellerId: string
  seller: User
  verificationStatus: VerificationStatus
  aiValidation?: AIValidation
  createdAt: Date
  updatedAt: Date
}

export type PropertyType = 'residential' | 'commercial' | 'agricultural' | 'industrial'
export type PropertyStatus = 'active' | 'sold' | 'pending' | 'inactive'
export type VerificationStatus = 'pending' | 'verified' | 'rejected'

export interface PropertyImage {
  id: string
  url: string
  caption?: string
  isPrimary: boolean
}

export interface PropertyDocument {
  id: string
  type: DocumentType
  url: string
  name: string
  verificationStatus: VerificationStatus
}

export type DocumentType = 'encumbrance_certificate' | 'property_tax_receipt' | 'survey_settlement' | 'other'

export interface AIValidation {
  score: number
  issues: string[]
  recommendations: string[]
  lastValidated: Date
}

// Broker types
export interface Broker {
  id: string
  userId: string
  user: User
  brokerId: string
  serviceAreas: string[]
  specializations: string[]
  experience: number
  rating: number
  reviewCount: number
  commission: number
  portfolio: PortfolioItem[]
  documents: BrokerDocument[]
  applicationStatus: ApplicationStatus
  createdAt: Date
  updatedAt: Date
}

export interface PortfolioItem {
  id: string
  title: string
  description: string
  images: string[]
  completedAt: Date
}

export interface BrokerDocument {
  id: string
  type: BrokerDocumentType
  url: string
  verificationStatus: VerificationStatus
}

export type BrokerDocumentType = 'aadhaar' | 'pan' | 'license' | 'other'

// Contractor types
export interface Contractor {
  id: string
  userId: string
  user: User
  contractorId: string
  specializations: string[]
  experience: number
  rating: number
  reviewCount: number
  serviceAreas: string[]
  portfolio: PortfolioItem[]
  documents: ContractorDocument[]
  applicationStatus: ApplicationStatus
  createdAt: Date
  updatedAt: Date
}

export interface ContractorDocument {
  id: string
  type: ContractorDocumentType
  url: string
  verificationStatus: VerificationStatus
}

export type ContractorDocumentType = 'aadhaar' | 'pan' | 'license' | 'certification' | 'other'

export type ApplicationStatus = 'pending' | 'approved' | 'rejected' | 'under_review'

// Chat types
export interface ChatRoom {
  id: string
  type: ChatType
  participants: User[]
  lastMessage?: Message
  metadata?: ChatMetadata
  createdAt: Date
  updatedAt: Date
}

export type ChatType = 'direct' | 'group' | 'property_discussion'

export interface ChatMetadata {
  propertyId?: string
  contractorId?: string
  projectId?: string
}

export interface Message {
  id: string
  roomId: string
  senderId: string
  sender: User
  content: string
  type: MessageType
  fileUrl?: string
  location?: Location
  replyTo?: string
  createdAt: Date
}

export type MessageType = 'text' | 'image' | 'file' | 'location'

// API Response types
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  name: string
  email: string
  phone: string
  password: string
  confirmPassword: string
  role: UserRole
}

export interface PropertyForm {
  title: string
  description: string
  type: PropertyType
  price: number
  area: number
  location: Location
  amenities: string[]
  images: File[]
  documents: File[]
}

// Filter types
export interface PropertyFilters {
  type?: PropertyType
  minPrice?: number
  maxPrice?: number
  minArea?: number
  maxArea?: number
  location?: string
  amenities?: string[]
}

export interface BrokerFilters {
  serviceAreas?: string[]
  specializations?: string[]
  minRating?: number
  experience?: number
}

export interface ContractorFilters {
  specializations?: string[]
  serviceAreas?: string[]
  minRating?: number
  experience?: number
}
