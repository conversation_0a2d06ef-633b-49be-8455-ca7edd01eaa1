# BUILD-CONNECT Website

A modern, responsive web application that mirrors the functionality and design of the BUILD-CONNECT mobile app - a comprehensive platform connecting property buyers, site scouts (brokers), contractors, and facilitating real estate transactions.

## 🚀 Features

### Core Features
- **Property Management**: Browse, search, and manage property listings
- **Site Scout Network**: Connect with verified brokers and site scouts
- **Contractor Directory**: Find skilled contractors for construction needs
- **Interactive Map Explorer**: Explore properties on an interactive map
- **Real-time Chat System**: Seamless communication between all parties
- **User Authentication**: Role-based authentication for buyers, brokers, and contractors

### Design System
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Modern UI**: Clean, intuitive interface based on the mobile app design
- **Dark/Light Theme**: Automatic theme switching support
- **Accessibility**: WCAG 2.1 AA compliant components

## 🛠 Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: TanStack Query for server state, <PERSON>ust<PERSON> for client state
- **UI Components**: Radix <PERSON> with custom styling
- **Forms**: React Hook Form with Yup validation
- **Authentication**: NextAuth.js (ready for implementation)
- **Real-time**: Socket.IO client (ready for implementation)

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd build-connect-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── auth/              # Authentication pages
│   ├── properties/        # Property listings and details
│   ├── brokers/           # Site scout directory
│   ├── contractors/       # Contractor directory
│   ├── map/               # Interactive map explorer
│   ├── chats/             # Real-time messaging
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/
│   ├── ui/                # Reusable UI components
│   ├── layout/            # Layout components
│   └── features/          # Feature-specific components
├── lib/
│   ├── constants.ts       # App constants and configuration
│   └── utils.ts           # Utility functions
├── types/
│   └── index.ts           # TypeScript type definitions
├── hooks/                 # Custom React hooks
├── stores/                # State management
└── styles/                # Additional styles
```

## 🎨 Design System

### Color Palette
The design system is based on the mobile app's color scheme:

- **Primary**: #2A8E9E (Teal)
- **Secondary**: #001d3d (Dark Blue)
- **Background**: #F5F7FA (Light Gray)
- **Accent**: #E6F3F7 (Light Teal)
- **Success**: #4CAF50 (Green)
- **Warning**: #FFB300 (Orange)
- **Error**: #D32F2F (Red)

### Typography
- **Primary Font**: Inter (Google Fonts)
- **Font Weights**: 300, 400, 500, 600, 700, 800, 900

### Components
All components follow the design patterns from the mobile app:
- Consistent spacing and sizing
- Rounded corners (8px, 12px)
- Subtle shadows and hover effects
- Focus states for accessibility

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Style
- TypeScript for type safety
- ESLint for code quality
- Prettier for code formatting (recommended)
- Consistent naming conventions

## 🚀 Deployment

The application is ready for deployment on platforms like:
- **Vercel** (recommended for Next.js)
- **Netlify**
- **AWS Amplify**
- **Railway**

### Environment Variables
Create a `.env.local` file for environment variables:

```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
DATABASE_URL=your-database-url
GOOGLE_MAPS_API_KEY=your-google-maps-key
```

## 🔮 Future Enhancements

### Phase 1 - Core Functionality
- [ ] Complete authentication system with NextAuth.js
- [ ] Database integration with Prisma
- [ ] API routes for CRUD operations
- [ ] Real-time chat with Socket.IO

### Phase 2 - Advanced Features
- [ ] Google Maps integration
- [ ] File upload and image optimization
- [ ] Payment gateway integration
- [ ] Email notifications

### Phase 3 - AI & Analytics
- [ ] AI-powered property recommendations
- [ ] Document verification system
- [ ] Analytics dashboard
- [ ] Advanced search and filtering

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**BUILD-CONNECT** - Connect. Build. Grow. 🏗️
