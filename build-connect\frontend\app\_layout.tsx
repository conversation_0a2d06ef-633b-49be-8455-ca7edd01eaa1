import { Stack } from 'expo-router';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Inter_900Black, useFonts } from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import { ThemeProvider } from '../context/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import ToastConfig from './Components/Shared/ToastConfig';
import { QueryClientProvider } from '@tanstack/react-query';
import queryClient from '../api/queryClient';

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
    const [loaded, error] = useFonts({
        Inter: Inter_900Black,
        playwrite: require('../assets/fonts/PlaywriteDKLoopet-Thin.ttf'),
    });

    useEffect(() => {
        const hideSplash = async () => {
            if (loaded || error) {
                try {
                    await SplashScreen.hideAsync();
                } catch (e) {
                    console.error(e);
                }
            }
        };
        hideSplash();
    }, [loaded, error]);

    if (!loaded && !error) {
        return null;
    }

    return (
        <QueryClientProvider client={queryClient}>
            <ThemeProvider>
                <SafeAreaView style={{ flex: 1 }}>
                    <GestureHandlerRootView style={{ flex: 1 }}>
                        <Stack screenOptions={{ headerShown: false }} />
                        <Toast config={ToastConfig} />
                    </GestureHandlerRootView>
                </SafeAreaView>
            </ThemeProvider>
        </QueryClientProvider>
    );
}
