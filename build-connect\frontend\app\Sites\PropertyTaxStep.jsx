import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';
import FilePicker from './FilePicker';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const PropertyTaxStep = ({ theme, formik, setStep, isSubmitting, onDocumentPreview }) => {
    const { values, errors, touched, handleChange, handleBlur, setFieldValue, validateForm, setFieldTouched } = formik;

    const handleNext = async () => {
        const errors = await validateForm();
        const fields = [
            'propertyTaxNumber',
            'propertyTaxDate',
            'propertyTaxRec',
        ];
        if (!fields.some((field) => errors[field])) {
            setStep('review');
        } else {
            fields.forEach((field) =>
                setFieldTouched(field, true)
            );
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Property Tax Receipt
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
                Provide property tax receipt details and upload the document
            </Text>

            {/* Property Tax Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.propertyTaxNumber && touched.propertyTaxNumber
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="receipt-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.propertyTaxNumber}
                    onChangeText={handleChange('propertyTaxNumber')}
                    onBlur={handleBlur('propertyTaxNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter property tax number"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Property tax number"
                />
            </View>
            {errors.propertyTaxNumber && touched.propertyTaxNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.propertyTaxNumber}
                </Text>
            )}

            {/* Property Tax Date */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.propertyTaxDate && touched.propertyTaxDate
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="calendar-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.propertyTaxDate}
                    onChangeText={handleChange('propertyTaxDate')}
                    onBlur={handleBlur('propertyTaxDate')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter property tax date (DD/MM/YYYY)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Property tax date"
                />
            </View>
            {errors.propertyTaxDate && touched.propertyTaxDate && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.propertyTaxDate}
                </Text>
            )}

            {/* Property Tax Receipt Upload */}
            <FilePicker
                label="Property Tax Receipt (JPG/PNG/PDF)"
                files={values.propertyTaxRec}
                setFiles={(file) => setFieldValue('propertyTaxRec', file)}
                keyName="propertyTaxRec"
                maxFiles={1}
                allowedTypes={ALLOWED_TYPES}
                maxFileSize={MAX_FILE_SIZE}
                isMultiple={false}
                theme={theme}
                onDocumentPreview={onDocumentPreview}
            />
            {errors.propertyTaxRec && touched.propertyTaxRec && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.propertyTaxRec}
                </Text>
            )}

            {/* Document Preview */}
            {values.propertyTaxRec && (
                <Pressable
                    style={[
                        styles.documentPreview,
                        { backgroundColor: theme.ACCENT, borderColor: theme.INPUT_BORDER }
                    ]}
                    onPress={() => onDocumentPreview(values.propertyTaxRec)}
                >
                    <Ionicons
                        name="receipt-outline"
                        size={24}
                        color={theme.PRIMARY}
                    />
                    <Text style={[styles.documentPreviewText, { color: theme.TEXT_PRIMARY }]}>
                        {values.propertyTaxRec.name || 'Property Tax Receipt'}
                    </Text>
                    <Ionicons
                        name="eye-outline"
                        size={20}
                        color={theme.PRIMARY}
                    />
                </Pressable>
            )}

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.backButton, { borderColor: theme.INPUT_BORDER }]}
                    onPress={() => setStep('encumbrance')}
                    disabled={isSubmitting}
                    accessibilityLabel="Go back to encumbrance details"
                    accessibilityRole="button"
                >
                    <Text style={[styles.backButtonText, { color: theme.PRIMARY }]}>
                        Back
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleNext}
                    disabled={isSubmitting}
                    accessibilityLabel="Proceed to review"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default PropertyTaxStep;
