'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function PropertiesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground">Properties</h1>
        <p className="mt-2 text-muted-foreground">
          Discover your perfect property from our extensive listings
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <Input placeholder="Search properties by location, type, or price..." />
        </div>
        <Button variant="outline">Filters</Button>
        <Button>Search</Button>
      </div>

      {/* Property Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Sample Property Cards */}
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="group cursor-pointer transition-all hover:shadow-card-hover">
            <div className="aspect-video w-full overflow-hidden rounded-t-lg bg-muted">
              <div className="flex h-full items-center justify-center text-muted-foreground">
                Property Image {i}
              </div>
            </div>
            <CardHeader>
              <CardTitle className="text-lg">Modern Villa in Bangalore</CardTitle>
              <CardDescription>3 BHK • 2000 sq ft • Whitefield</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-primary">₹85 Lakhs</span>
                <Button size="sm">View Details</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="mt-12 text-center">
        <Button variant="outline" size="lg">
          Load More Properties
        </Button>
      </div>
    </div>
  )
}
