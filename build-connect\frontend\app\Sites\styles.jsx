import { StyleSheet, Dimensions } from 'react-native';

const { height } = Dimensions.get('window');

export const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'center',
    },
    backgroundImage: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100%',
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        marginHorizontal: '5%',
        marginVertical: 40,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 12,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        paddingHorizontal: 12,
        height: 56,
    },
    disabledInputContainer: {
        opacity: 0.7,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    disabledInput: {
        opacity: 0.6,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
        marginTop: 16,
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginBottom: 8,
        marginLeft: 4,
    },
    mapContainer: {
        marginVertical: 16,
    },
    fileButton: {
        borderWidth: 1,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        alignItems: 'center',
        flexDirection: 'row',
    },
    fileButtonText: {
        fontSize: 16,
        flex: 1,
    },
    documentPreview: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        padding: 12,
        marginBottom: 16,
    },
    documentPreviewText: {
        flex: 1,
        fontSize: 14,
        marginLeft: 8,
    },
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        marginTop: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
        flex: 1,
        marginLeft: 8,
    },
    submitButtonGradient: {
        paddingVertical: 16,
        paddingHorizontal: 24,
        alignItems: 'center',
    },
    submitButtonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    backButton: {
        borderRadius: 12,
        borderWidth: 2,
        paddingVertical: 16,
        paddingHorizontal: 24,
        alignItems: 'center',
        flex: 1,
        marginRight: 8,
    },
    backButtonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    buttonContainer: {
        flexDirection: 'row',
        marginTop: 24,
    },
    reviewContainer: {
        maxHeight: 300,
        marginBottom: 16,
    },
    reviewSection: {
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        borderWidth: 1,
    },
    reviewSectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    reviewSectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    reviewItem: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    reviewLabel: {
        fontSize: 14,
        fontWeight: '600',
        width: 120,
    },
    reviewValue: {
        fontSize: 14,
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 16,
        marginTop: 16,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 24,
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
    },
    // Legacy styles for compatibility with existing components
    h1: {
        fontSize: 28,
        fontWeight: '700',
        color: '#333',
        marginBottom: 24,
        textAlign: 'center',
    },
    card: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    section: {
        fontSize: 18,
        fontWeight: '600',
        color: '#008060',
        marginBottom: 16,
    },
    nextButton: {
        backgroundColor: '#008060',
        borderRadius: 8,
        padding: 16,
        alignItems: 'center',
        flex: 1,
        marginLeft: 8,
    },
    nextButtonText: {
        fontSize: 18,
        fontWeight: '600',
        color: '#fff',
    },
    reviewText: {
        fontSize: 16,
        color: '#333',
        marginBottom: 16,
    },
});

export default styles;
