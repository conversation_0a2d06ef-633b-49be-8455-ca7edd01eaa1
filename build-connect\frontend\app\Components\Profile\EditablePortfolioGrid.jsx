import React, { useContext, useState } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    FlatList,
    Modal,
    ScrollView,
    Dimensions,
    TextInput,
    Alert,
    ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ThemeContext } from '../../../context/ThemeContext';
import {
    addPortfolioItem,
    updatePortfolioItem,
    deletePortfolioItem,
} from '../../../api/contractor/contractorApi';
import { showToast } from '../../../utils/showToast';

const { width } = Dimensions.get('window');
const itemSize = (width - 48) / 3; // 3 items per row with padding

export default function EditablePortfolioGrid({
    portfolio = [],
    contractorId,
}) {
    const { theme } = useContext(ThemeContext);
    const queryClient = useQueryClient();
    const [selectedItem, setSelectedItem] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [editingItem, setEditingItem] = useState(null);
    const [newCaption, setNewCaption] = useState('');
    const [newImage, setNewImage] = useState(null);

    // Mutations
    const addMutation = useMutation({
        mutationFn: addPortfolioItem,
        onSuccess: () => {
            queryClient.invalidateQueries(['contractorProfile', contractorId]);
            setAddModalVisible(false);
            setNewCaption('');
            setNewImage(null);
            showToast(
                'success',
                'Success',
                'Portfolio item added successfully!'
            );
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.response?.data?.message || 'Failed to add portfolio item'
            );
        },
    });

    const updateMutation = useMutation({
        mutationFn: ({ portfolioItemId, data }) =>
            updatePortfolioItem(portfolioItemId, data),
        onSuccess: () => {
            queryClient.invalidateQueries(['contractorProfile', contractorId]);
            setEditModalVisible(false);
            setEditingItem(null);
            setNewCaption('');
            setNewImage(null);
            showToast(
                'success',
                'Success',
                'Portfolio item updated successfully!'
            );
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.response?.data?.message ||
                    'Failed to update portfolio item'
            );
        },
    });

    const deleteMutation = useMutation({
        mutationFn: deletePortfolioItem,
        onSuccess: () => {
            queryClient.invalidateQueries(['contractorProfile', contractorId]);
            showToast(
                'success',
                'Success',
                'Portfolio item deleted successfully!'
            );
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.response?.data?.message ||
                    'Failed to delete portfolio item'
            );
        },
    });

    const openModal = (item) => {
        setSelectedItem(item);
        setModalVisible(true);
    };

    const closeModal = () => {
        setModalVisible(false);
        setSelectedItem(null);
    };

    const openEditModal = (item) => {
        setEditingItem(item);
        setNewCaption(item.caption);
        setEditModalVisible(true);
        closeModal();
    };

    const openAddModal = () => {
        setNewCaption('');
        setNewImage(null);
        setAddModalVisible(true);
    };

    const pickImage = async () => {
        const { status } =
            await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
            showToast(
                'error',
                'Permission Required',
                'Camera roll permission is required to select images'
            );
            return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [1, 1],
            quality: 0.8,
        });

        if (!result.canceled) {
            setNewImage(result.assets[0]);
        }
    };

    const handleAddPortfolioItem = () => {
        if (!newImage) {
            showToast('error', 'Error', 'Please select an image');
            return;
        }
        if (!newCaption.trim()) {
            showToast('error', 'Error', 'Please enter a caption');
            return;
        }

        const formData = new FormData();
        formData.append('portfolioImage', {
            uri: newImage.uri,
            type: 'image/jpeg',
            name: 'portfolio.jpg',
        });
        formData.append('caption', newCaption.trim());
        addMutation.mutate(formData);
    };
    const handleUpdatePortfolioItem = () => {
        if (!newCaption.trim()) {
            showToast('error', 'Error', 'Please enter a caption');
            return;
        }

        const formData = new FormData();
        if (newImage) {
            formData.append('portfolioImage', {
                uri: newImage.uri,
                type: 'image/jpeg',
                name: 'portfolio.jpg',
            });
        }
        formData.append('caption', newCaption.trim());

        updateMutation.mutate({
            portfolioItemId: editingItem._id,
            data: formData,
        });
    };

    const handleDeletePortfolioItem = (item) => {
        Alert.alert(
            'Delete Portfolio Item',
            'Are you sure you want to delete this portfolio item?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => deleteMutation.mutate(item._id),
                },
            ]
        );
    };

    const renderPortfolioItem = ({ item }) => (
        <TouchableOpacity
            style={[styles.portfolioItem, { backgroundColor: theme.CARD }]}
            onPress={() => openModal(item)}
            activeOpacity={0.7}
        >
            <Image
                source={{ uri: item.image }}
                style={styles.portfolioImage}
                resizeMode="cover"
            />
            <View style={styles.portfolioOverlay}>
                <Ionicons name="expand" size={20} color="white" />
            </View>
        </TouchableOpacity>
    );

    const renderAddButton = () => (
        <TouchableOpacity
            style={[
                styles.addButton,
                {
                    backgroundColor: theme.INPUT_BACKGROUND,
                    borderColor: theme.BORDER,
                },
            ]}
            onPress={openAddModal}
            activeOpacity={0.7}
        >
            <Ionicons name="add" size={30} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
    );

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const portfolioData = [...portfolio, { isAddButton: true }];

    return (
        <View style={styles.container}>
            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                Portfolio ({portfolio.length})
            </Text>

            <FlatList
                data={portfolioData}
                renderItem={({ item, index }) =>
                    item.isAddButton
                        ? renderAddButton()
                        : renderPortfolioItem({ item, index })
                }
                keyExtractor={(item, index) =>
                    item._id || `add-button-${index}`
                }
                numColumns={3}
                scrollEnabled={false}
                contentContainerStyle={styles.grid}
                columnWrapperStyle={styles.row}
            />

            {/* Portfolio Detail Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={closeModal}
            >
                <View style={styles.modalOverlay}>
                    <View
                        style={[
                            styles.modalContent,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <View style={styles.modalHeader}>
                            <TouchableOpacity
                                style={styles.modalButton}
                                onPress={() => openEditModal(selectedItem)}
                            >
                                <Ionicons
                                    name="create-outline"
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.modalButton}
                                onPress={() =>
                                    handleDeletePortfolioItem(selectedItem)
                                }
                            >
                                <Ionicons
                                    name="trash-outline"
                                    size={24}
                                    color="#F44336"
                                />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={closeModal}
                            >
                                <Ionicons
                                    name="close"
                                    size={24}
                                    color={theme.TEXT_PRIMARY}
                                />
                            </TouchableOpacity>
                        </View>

                        <ScrollView showsVerticalScrollIndicator={false}>
                            {selectedItem && (
                                <>
                                    <Image
                                        source={{ uri: selectedItem.image }}
                                        style={styles.modalImage}
                                        resizeMode="cover"
                                    />
                                    <View style={styles.modalDetails}>
                                        <Text
                                            style={[
                                                styles.modalCaption,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            {selectedItem.caption}
                                        </Text>
                                        <View style={styles.modalMeta}>
                                            <Ionicons
                                                name="calendar-outline"
                                                size={16}
                                                color={theme.TEXT_SECONDARY}
                                            />
                                            <Text
                                                style={[
                                                    styles.modalDate,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                {formatDate(
                                                    selectedItem.createdAt
                                                )}
                                            </Text>
                                        </View>
                                    </View>
                                </>
                            )}
                        </ScrollView>
                    </View>
                </View>
            </Modal>

            {/* Edit Modal */}
            <Modal
                visible={editModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setEditModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View
                        style={[
                            styles.editModalContent,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <View style={styles.editModalHeader}>
                            <Text
                                style={[
                                    styles.editModalTitle,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Edit Portfolio Item
                            </Text>
                            <TouchableOpacity
                                onPress={() => setEditModalVisible(false)}
                                style={styles.closeButton}
                            >
                                <Ionicons
                                    name="close"
                                    size={24}
                                    color={theme.TEXT_PRIMARY}
                                />
                            </TouchableOpacity>
                        </View>

                        <ScrollView style={styles.editModalBody}>
                            {/* Current Image */}
                            {editingItem && (
                                <Image
                                    source={{ uri: editingItem.image }}
                                    style={styles.editImage}
                                    resizeMode="cover"
                                />
                            )}

                            {/* New Image Preview */}
                            {newImage && (
                                <Image
                                    source={{ uri: newImage.uri }}
                                    style={styles.editImage}
                                    resizeMode="cover"
                                />
                            )}

                            {/* Change Image Button */}
                            <TouchableOpacity
                                style={[
                                    styles.changeImageButton,
                                    { backgroundColor: theme.INPUT_BACKGROUND },
                                ]}
                                onPress={pickImage}
                            >
                                <Ionicons
                                    name="camera-outline"
                                    size={20}
                                    color={theme.TEXT_PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.changeImageText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Change Image
                                </Text>
                            </TouchableOpacity>

                            {/* Caption Input */}
                            <Text
                                style={[
                                    styles.inputLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Caption
                            </Text>
                            <TextInput
                                style={[
                                    styles.captionInput,
                                    {
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                        color: theme.TEXT_PRIMARY,
                                        borderColor: theme.BORDER,
                                    },
                                ]}
                                value={newCaption}
                                onChangeText={setNewCaption}
                                placeholder="Enter caption..."
                                placeholderTextColor={theme.TEXT_SECONDARY}
                                multiline
                                numberOfLines={3}
                            />
                        </ScrollView>

                        <View style={styles.editModalFooter}>
                            <TouchableOpacity
                                style={[
                                    styles.cancelButton,
                                    { backgroundColor: theme.INPUT_BACKGROUND },
                                ]}
                                onPress={() => setEditModalVisible(false)}
                            >
                                <Text
                                    style={[
                                        styles.cancelButtonText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Cancel
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.saveButton,
                                    { backgroundColor: theme.PRIMARY },
                                ]}
                                onPress={handleUpdatePortfolioItem}
                                disabled={updateMutation.isPending}
                            >
                                {updateMutation.isPending ? (
                                    <ActivityIndicator
                                        color="white"
                                        size="small"
                                    />
                                ) : (
                                    <Text style={styles.saveButtonText}>
                                        Save
                                    </Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>

            {/* Add Modal */}
            <Modal
                visible={addModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setAddModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View
                        style={[
                            styles.editModalContent,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <View style={styles.editModalHeader}>
                            <Text
                                style={[
                                    styles.editModalTitle,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Add Portfolio Item
                            </Text>
                            <TouchableOpacity
                                onPress={() => setAddModalVisible(false)}
                                style={styles.closeButton}
                            >
                                <Ionicons
                                    name="close"
                                    size={24}
                                    color={theme.TEXT_PRIMARY}
                                />
                            </TouchableOpacity>
                        </View>

                        <ScrollView style={styles.editModalBody}>
                            {/* Image Preview */}
                            {newImage ? (
                                <Image
                                    source={{ uri: newImage.uri }}
                                    style={styles.editImage}
                                    resizeMode="cover"
                                />
                            ) : (
                                <View
                                    style={[
                                        styles.imagePlaceholder,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            borderColor: theme.BORDER,
                                        },
                                    ]}
                                >
                                    <Ionicons
                                        name="image-outline"
                                        size={40}
                                        color={theme.TEXT_SECONDARY}
                                    />
                                    <Text
                                        style={[
                                            styles.placeholderText,
                                            { color: theme.TEXT_SECONDARY },
                                        ]}
                                    >
                                        Select an image
                                    </Text>
                                </View>
                            )}

                            {/* Select Image Button */}
                            <TouchableOpacity
                                style={[
                                    styles.changeImageButton,
                                    { backgroundColor: theme.INPUT_BACKGROUND },
                                ]}
                                onPress={pickImage}
                            >
                                <Ionicons
                                    name="camera-outline"
                                    size={20}
                                    color={theme.TEXT_PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.changeImageText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Select Image
                                </Text>
                            </TouchableOpacity>

                            {/* Caption Input */}
                            <Text
                                style={[
                                    styles.inputLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Caption
                            </Text>
                            <TextInput
                                style={[
                                    styles.captionInput,
                                    {
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                        color: theme.TEXT_PRIMARY,
                                        borderColor: theme.BORDER,
                                    },
                                ]}
                                value={newCaption}
                                onChangeText={setNewCaption}
                                placeholder="Enter caption..."
                                placeholderTextColor={theme.TEXT_SECONDARY}
                                multiline
                                numberOfLines={3}
                            />
                        </ScrollView>

                        <View style={styles.editModalFooter}>
                            <TouchableOpacity
                                style={[
                                    styles.cancelButton,
                                    { backgroundColor: theme.INPUT_BACKGROUND },
                                ]}
                                onPress={() => setAddModalVisible(false)}
                            >
                                <Text
                                    style={[
                                        styles.cancelButtonText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Cancel
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.saveButton,
                                    { backgroundColor: theme.PRIMARY },
                                ]}
                                onPress={handleAddPortfolioItem}
                                disabled={addMutation.isPending}
                            >
                                {addMutation.isPending ? (
                                    <ActivityIndicator
                                        color="white"
                                        size="small"
                                    />
                                ) : (
                                    <Text style={styles.saveButtonText}>
                                        Add
                                    </Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginHorizontal: 16,
        marginBottom: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    grid: {
        paddingBottom: 16,
    },
    row: {
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    portfolioItem: {
        width: itemSize,
        height: itemSize,
        borderRadius: 8,
        overflow: 'hidden',
        position: 'relative',
    },
    portfolioImage: {
        width: '100%',
        height: '100%',
    },
    portfolioOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    addButton: {
        width: itemSize,
        height: itemSize,
        borderRadius: 8,
        borderWidth: 2,
        borderStyle: 'dashed',
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: width * 0.9,
        maxHeight: '80%',
        borderRadius: 12,
        overflow: 'hidden',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        padding: 16,
        paddingBottom: 8,
    },
    modalButton: {
        padding: 8,
        marginRight: 8,
    },
    closeButton: {
        padding: 8,
    },
    modalImage: {
        width: '100%',
        height: 300,
    },
    modalDetails: {
        padding: 16,
    },
    modalCaption: {
        fontSize: 16,
        lineHeight: 24,
        marginBottom: 12,
    },
    modalMeta: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    modalDate: {
        fontSize: 14,
        marginLeft: 6,
    },
    editModalContent: {
        width: width * 0.9,
        maxHeight: '90%',
        borderRadius: 12,
        overflow: 'hidden',
    },
    editModalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    editModalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    editModalBody: {
        padding: 16,
        maxHeight: 400,
    },
    editImage: {
        width: '100%',
        height: 200,
        borderRadius: 8,
        marginBottom: 16,
    },
    imagePlaceholder: {
        width: '100%',
        height: 200,
        borderRadius: 8,
        borderWidth: 2,
        borderStyle: 'dashed',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
    },
    placeholderText: {
        marginTop: 8,
        fontSize: 16,
    },
    changeImageButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 12,
        borderRadius: 8,
        marginBottom: 16,
    },
    changeImageText: {
        marginLeft: 8,
        fontSize: 16,
        fontWeight: '500',
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 8,
    },
    captionInput: {
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        textAlignVertical: 'top',
        minHeight: 80,
    },
    editModalFooter: {
        flexDirection: 'row',
        padding: 16,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    cancelButton: {
        flex: 1,
        padding: 12,
        borderRadius: 8,
        alignItems: 'center',
        marginRight: 8,
    },
    cancelButtonText: {
        fontSize: 16,
        fontWeight: '500',
    },
    saveButton: {
        flex: 1,
        padding: 12,
        borderRadius: 8,
        alignItems: 'center',
        marginLeft: 8,
    },
    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
    },
});
