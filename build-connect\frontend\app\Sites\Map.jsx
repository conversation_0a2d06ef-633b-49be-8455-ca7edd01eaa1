import React, { useEffect, useState } from 'react';
import { Dimensions, StyleSheet, Alert } from 'react-native';
import { View, Text } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { privateAPIClient } from '../../api';

const MapScreen = () => {
  const [hasPermission, setHasPermission] = useState(false);
  const [sites, setSites] = useState([]);
  const [region, setRegion] = useState({
    latitude: 20.5937, // Default to India’s center
    longitude: 78.9629,
    latitudeDelta: 100.1922,
    longitudeDelta: 100.1421,
  });

  useEffect(() => {
    (async () => {
      try {
        // Request location permission
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Denied', 'Please allow location access to use this feature.');
          setHasPermission(false);
          return;
        }
        setHasPermission(true);

        // Fetch user location
        let currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        const { latitude, longitude } = currentLocation.coords;

        // Update region to center on user’s location
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.05, // Smaller delta for a closer zoom
          longitudeDelta: 0.05,
        });

        // Fetch sites using the current location
        const response = await privateAPIClient.get(
          `/site-service/api/v1/sites?latitude=${latitude}&longitude=${longitude}&distance=20000`
        );
        const data = response.data.sites || []; // Fallback to empty array if no sites
        setSites(data);
        console.log(data);

        if (data.length > 0) {
          console.log('First site ID:', data[0]._id);
        } else {
          console.log('No sites found within the specified distance.');
        }
      } catch (error) {
        console.error('Error fetching location or sites:', error);
        Alert.alert('Error', 'Failed to fetch location or sites. Please try again.');
      }
    })();
  }, []);

  return (
    <View style={styles.container}>
      {hasPermission ? (
        <MapView
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          showsUserLocation={true} // Show blue dot for user location
          region={region}
          onRegionChangeComplete={setRegion} // Update region when user pans/zooms
        >
          {sites.map((site) => (
            <Marker
              key={site._id}
              coordinate={{ latitude: site.latitude, longitude: site.longitude }}
              title={site.name}
              description={site.description || 'No description available'} // Optional description
            />
          ))}
        </MapView>
      ) : (
        <Text style={styles.errorText}>Location permission is required to display the map.</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height*0.5,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    margin: 20,
  },
});

export default MapScreen;